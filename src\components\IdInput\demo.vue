<template>
  <div class="id-input-demo p-6 max-w-4xl mx-auto bg-white">
    <h1 class="text-3xl font-bold text-gray-800 mb-8">ID输入框组件演示</h1>

    <!-- 功能说明 -->
    <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-8">
      <h2 class="text-lg font-semibold text-blue-800 mb-2">功能特性</h2>
      <ul class="text-blue-700 space-y-1">
        <li>• 只允许输入字母、数字、下划线(_)、横线(-)</li>
        <li>• 自动将小写字母转换为大写</li>
        <li>• 智能处理所有输入内容（包括粘贴、中文输入等）</li>
        <li>• 使用 useAttrs 和 useSlots 完全转发属性和插槽</li>
        <li>• 提供便捷的操作方法</li>
      </ul>
    </div>

    <!-- 基础用法 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">基础用法</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            基础输入框
          </label>
          <IdInput
            v-model="basicValue"
            placeholder="请输入ID（只能包含字母、数字、_、-）"
            class="w-full"
          />
          <p class="text-sm text-gray-500 mt-1">当前值: {{ basicValue }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            带最大长度限制
          </label>
          <IdInput
            v-model="limitedValue"
            placeholder="最多10个字符"
            :maxLength="10"
            class="w-full"
          />
          <p class="text-sm text-gray-500 mt-1">
            当前值: {{ limitedValue }} ({{ limitedValue.length }}/10)
          </p>
        </div>
      </div>
    </div>

    <!-- 不同状态 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">不同状态</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            禁用状态
          </label>
          <IdInput
            v-model="disabledValue"
            placeholder="禁用状态"
            disabled
            class="w-full"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            只读状态
          </label>
          <IdInput v-model="readonlyValue" readonly class="w-full" />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            错误状态
          </label>
          <IdInput
            v-model="invalidValue"
            placeholder="错误状态"
            invalid
            class="w-full"
          />
        </div>
      </div>
    </div>

    <!-- 不同尺寸 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">不同尺寸</h2>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            小尺寸
          </label>
          <IdInput
            v-model="smallValue"
            placeholder="小尺寸输入框"
            size="small"
            class="w-full max-w-md"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            默认尺寸
          </label>
          <IdInput
            v-model="normalValue"
            placeholder="默认尺寸输入框"
            class="w-full max-w-md"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            大尺寸
          </label>
          <IdInput
            v-model="largeValue"
            placeholder="大尺寸输入框"
            size="large"
            class="w-full max-w-md"
          />
        </div>
      </div>
    </div>

    <!-- 输入测试 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">输入测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            综合输入测试
          </label>
          <IdInput
            v-model="testValue"
            placeholder="尝试输入、粘贴各种内容"
            class="w-full mb-2"
          />
          <div class="text-xs text-gray-600 space-y-1">
            <p>• 尝试输入: "hello@world#123"</p>
            <p>• 尝试粘贴: "用户名_123-abc"</p>
            <p>• 尝试输入中文: "测试test"</p>
            <p>• 所有内容都会自动过滤和转换</p>
          </div>
          <p class="text-sm text-gray-500 mt-2">当前值: {{ testValue }}</p>
        </div>

        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-2">测试用例</h3>
          <div class="space-y-2 text-sm">
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>输入: "hello123"</span>
              <span class="font-mono text-green-600">→ "HELLO123"</span>
            </div>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>输入: "test_id-01"</span>
              <span class="font-mono text-green-600">→ "TEST_ID-01"</span>
            </div>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>粘贴: "abc@#$123"</span>
              <span class="font-mono text-green-600">→ "ABC123"</span>
            </div>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>中文: "用户名_01"</span>
              <span class="font-mono text-green-600">→ "_01"</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件测试 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">事件测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            事件监听
          </label>
          <IdInput
            v-model="eventValue"
            placeholder="输入内容测试事件"
            class="w-full"
            @change="onChange"
            @focus="onFocus"
            @blur="onBlur"
          />
        </div>

        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-2">事件日志</h3>
          <div class="bg-gray-50 border rounded-md p-3 h-32 overflow-y-auto">
            <div
              v-for="(log, index) in eventLogs"
              :key="index"
              class="text-xs text-gray-600"
            >
              {{ log }}
            </div>
          </div>
          <button
            @click="clearLogs"
            class="mt-2 px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清空日志
          </button>
        </div>
      </div>
    </div>

    <!-- 方法测试 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">方法测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            方法操作测试
          </label>
          <IdInput
            ref="methodTestRef"
            v-model="methodValue"
            placeholder="测试各种方法"
            class="w-full mb-3"
          />
          <div class="flex flex-wrap gap-2">
            <button
              @click="focusInput"
              class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              聚焦
            </button>
            <button
              @click="blurInput"
              class="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              失焦
            </button>
            <button
              @click="selectAll"
              class="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
            >
              全选
            </button>
            <button
              @click="setCursor"
              class="px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              设置光标到开头
            </button>
          </div>
        </div>

        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-2">测试用例</h3>
          <div class="space-y-2 text-sm">
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>输入: "hello123"</span>
              <span class="font-mono text-green-600">→ "HELLO123"</span>
            </div>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>输入: "test_id-01"</span>
              <span class="font-mono text-green-600">→ "TEST_ID-01"</span>
            </div>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>输入: "abc@#$123"</span>
              <span class="font-mono text-green-600">→ "ABC123"</span>
            </div>
            <div
              class="flex items-center justify-between p-2 bg-gray-50 rounded"
            >
              <span>输入: "user-name_01"</span>
              <span class="font-mono text-green-600">→ "USER-NAME_01"</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单集成示例 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">表单集成示例</h2>
      <form @submit.prevent="submitForm" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              用户ID *
            </label>
            <IdInput
              v-model="formData.userId"
              placeholder="请输入用户ID"
              :invalid="!formData.userId && submitted"
              class="w-full"
              required
            />
            <p
              v-if="!formData.userId && submitted"
              class="text-red-500 text-xs mt-1"
            >
              用户ID不能为空
            </p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              项目代码
            </label>
            <IdInput
              v-model="formData.projectCode"
              placeholder="请输入项目代码"
              :maxLength="20"
              class="w-full"
            />
          </div>
        </div>

        <div class="flex gap-3">
          <button
            type="submit"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            提交表单
          </button>
          <button
            type="button"
            @click="resetForm"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            重置
          </button>
        </div>

        <div
          v-if="submitted"
          class="mt-4 p-3 bg-green-50 border border-green-200 rounded"
        >
          <h3 class="text-sm font-medium text-green-800 mb-2">提交的数据：</h3>
          <pre class="text-xs text-green-700">{{
            JSON.stringify(formData, null, 2)
          }}</pre>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import IdInput from "./index.vue";

// ==================== 响应式数据 ====================
const basicValue = ref("");
const limitedValue = ref("");
const disabledValue = ref("DISABLED_VALUE");
const readonlyValue = ref("READONLY_VALUE");
const invalidValue = ref("");
const smallValue = ref("");
const normalValue = ref("");
const largeValue = ref("");
const eventValue = ref("");
const methodValue = ref("TEST_METHOD_VALUE");
const testValue = ref("");

// 事件日志
const eventLogs = ref<string[]>([]);

// 表单数据
const formData = reactive({
  userId: "",
  projectCode: "",
});
const submitted = ref(false);

// 方法测试引用
const methodTestRef = ref();

// ==================== 事件处理 ====================
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  eventLogs.value.unshift(`[${timestamp}] ${message}`);
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10);
  }
};

const onChange = (event: Event) => {
  addLog(`Change事件: ${(event.target as HTMLInputElement).value}`);
};

const onFocus = () => {
  addLog("Focus事件: 输入框获得焦点");
};

const onBlur = () => {
  addLog("Blur事件: 输入框失去焦点");
};

const clearLogs = () => {
  eventLogs.value = [];
};

// ==================== 方法测试 ====================
const focusInput = () => {
  methodTestRef.value?.focus();
  addLog("调用focus()方法");
};

const blurInput = () => {
  methodTestRef.value?.blur();
  addLog("调用blur()方法");
};

const selectAll = () => {
  methodTestRef.value?.select();
  addLog("调用select()方法");
};

const setCursor = () => {
  methodTestRef.value?.setSelectionRange(0, 0);
  addLog("调用setSelectionRange(0, 0)方法");
};

// ==================== 表单处理 ====================
const submitForm = () => {
  submitted.value = true;
  if (formData.userId) {
    addLog(`表单提交成功: ${JSON.stringify(formData)}`);
  }
};

const resetForm = () => {
  formData.userId = "";
  formData.projectCode = "";
  submitted.value = false;
  addLog("表单已重置");
};
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
