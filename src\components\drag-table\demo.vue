<template>
  <div class="demo-container">
    <h2>可拖拽表格演示</h2>
    <p>
      说明：行1、2、3、4、5、6，其中3、4为disabled状态，将1拖拽到4后面，顺序应该为2、5、3、4、1、6
    </p>

    <div class="controls">
      <Button @click="resetData" label="重置数据" class="p-button-secondary" />
      <Button
        @click="toggleDisabled"
        label="切换3、4行状态"
        class="p-button-info"
      />
    </div>

    <DragTable
      :data="tableData"
      :columns="columns"
      :disabled-field="'disabled'"
      @drag-end="onDragEnd"
      class="demo-table"
    >
      <template #status="{ data }">
        <Tag
          :value="data.disabled ? '禁用' : '启用'"
          :severity="data.disabled ? 'danger' : 'success'"
        />
      </template>

      <template #actions="{ data, index }">
        <Button
          label="编辑"
          class="p-button-rounded p-button-text p-button-sm"
          @click="editRow(index)"
        />
        <Button
          label="删除"
          class="p-button-rounded p-button-text p-button-sm p-button-danger"
          @click="deleteRow(index)"
        />
      </template>
    </DragTable>

    <div class="result-section">
      <h3>拖拽结果</h3>
      <pre>{{ JSON.stringify(tableData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import DragTable from "./index.vue";
import Button from "primevue/button";
import Tag from "primevue/tag";

// 表格列定义
const columns = [
  { field: "id", header: "ID", sortable: true },
  { field: "name", header: "名称", sortable: true },
  { field: "description", header: "描述" },
  { field: "status", header: "状态", bodyTemplate: "status" },
  { field: "actions", header: "操作", bodyTemplate: "actions" },
];

// 初始数据
const initialData = [
  { id: 1, name: "项目1", description: "这是第一个项目", disabled: false },
  { id: 2, name: "项目2", description: "这是第二个项目", disabled: false },
  {
    id: 3,
    name: "项目3",
    description: "这是第三个项目（禁用）",
    disabled: true,
  },
  {
    id: 4,
    name: "项目4",
    description: "这是第四个项目（禁用）",
    disabled: true,
  },
  { id: 5, name: "项目5", description: "这是第五个项目", disabled: false },
  { id: 6, name: "项目6", description: "这是第六个项目", disabled: false },
];

const tableData = ref([...initialData]);

// 重置数据
const resetData = () => {
  tableData.value = [...initialData];
};

// 切换3、4行的disabled状态
const toggleDisabled = () => {
  tableData.value.forEach((item, index) => {
    if (index === 2 || index === 3) {
      // 第3、4行（索引2、3）
      item.disabled = !item.disabled;
    }
  });
};

// 拖拽结束事件
const onDragEnd = (newData: any[]) => {
  console.log("拖拽结束，新数据：", newData);
  tableData.value = newData;
};

// 编辑行
const editRow = (index: number) => {
  console.log("编辑行：", index, tableData.value[index]);
};

// 删除行
const deleteRow = (index: number) => {
  if (confirm("确定要删除这一行吗？")) {
    tableData.value.splice(index, 1);
  }
};
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.demo-table {
  margin: 20px 0;
}

.result-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.result-section h3 {
  margin-top: 0;
  color: #495057;
}

.result-section pre {
  background-color: #ffffff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
