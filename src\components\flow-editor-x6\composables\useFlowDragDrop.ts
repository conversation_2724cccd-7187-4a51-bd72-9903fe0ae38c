import { ref, type Ref } from "vue";
import type { Graph } from "@antv/x6";
import type { DragData, FlowPosition } from "../types";
import { createNodeFromType } from "../utils/nodeFactory";
import { nodeTypeRegistry } from "../nodes/simpleNodeTypes";

export function useFlowDragDrop(graph: Ref<Graph | undefined>, flowStore: any) {
  const isDragging = ref(false);
  const dragData = ref<DragData | null>(null);
  const dragPreview = ref<HTMLElement | null>(null);

  /**
   * 开始拖拽
   */
  const handleDragStart = (data: DragData, event: DragEvent) => {
    isDragging.value = true;
    dragData.value = data;

    // 设置拖拽数据
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = "copy";
      event.dataTransfer.setData("application/json", JSON.stringify(data));
    }

    // 创建拖拽预览
    createDragPreview(data, event);
  };

  /**
   * 创建拖拽预览
   */
  const createDragPreview = (data: DragData, event: DragEvent) => {
    if (data.type !== "node" || !data.nodeType) return;

    const nodeType = nodeTypeRegistry.get(data.nodeType);
    if (!nodeType) return;

    // 创建预览元素
    const preview = document.createElement("div");
    preview.className = "drag-preview";
    preview.style.cssText = `
      position: fixed;
      top: -1000px;
      left: -1000px;
      width: ${nodeType.defaultSize?.width || 120}px;
      height: ${nodeType.defaultSize?.height || 60}px;
      background: ${nodeType.color || "#ffffff"};
      border: 2px solid #333;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #333;
      pointer-events: none;
      z-index: 9999;
      opacity: 0.8;
    `;
    preview.textContent = nodeType.name;

    document.body.appendChild(preview);
    dragPreview.value = preview;

    // 设置拖拽图像
    if (event.dataTransfer) {
      event.dataTransfer.setDragImage(
        preview,
        (nodeType.defaultSize?.width || 120) / 2,
        (nodeType.defaultSize?.height || 60) / 2
      );
    }

    // 清理预览元素
    setTimeout(() => {
      if (preview.parentNode) {
        preview.parentNode.removeChild(preview);
      }
      dragPreview.value = null;
    }, 0);
  };

  /**
   * 拖拽进入画布
   */
  const handleDragEnter = (event: DragEvent) => {
    event.preventDefault();
    event.dataTransfer!.dropEffect = "copy";
  };

  /**
   * 拖拽在画布上移动
   */
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    event.dataTransfer!.dropEffect = "copy";
  };

  /**
   * 拖拽离开画布
   */
  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
  };

  /**
   * 在画布上放置
   */
  const handleDrop = (event: DragEvent) => {
    event.preventDefault();

    if (!graph.value) return;

    try {
      // 获取拖拽数据
      const dataStr = event.dataTransfer?.getData("application/json");
      if (!dataStr) return;

      const data: DragData = JSON.parse(dataStr);

      if (data.type === "node" && data.nodeType) {
        // 计算放置位置
        const position = getDropPosition(event);

        // 创建节点
        createNodeAtPosition(data.nodeType, position, data.data);
      }
    } catch (error) {
      console.error("Drop failed:", error);
    } finally {
      isDragging.value = false;
      dragData.value = null;
    }
  };

  /**
   * 获取放置位置
   */
  const getDropPosition = (event: DragEvent): FlowPosition => {
    if (!graph.value) return { x: 0, y: 0 };

    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 转换为图形坐标
    const point = graph.value.clientToLocal({ x, y });

    return {
      x: Math.round(point.x),
      y: Math.round(point.y),
    };
  };

  /**
   * 在指定位置创建节点
   */
  const createNodeAtPosition = (
    nodeTypeId: string,
    position: FlowPosition,
    customData?: Record<string, any>
  ) => {
    const nodeType = nodeTypeRegistry.get(nodeTypeId);
    if (!nodeType) {
      console.error(`Node type ${nodeTypeId} not found`);
      return;
    }

    // 创建流程节点
    const flowNode = createNodeFromType(nodeType, position, customData);

    // 添加到store
    flowStore.addNode(flowNode);

    // 添加到图形
    if (graph.value) {
      const x6Node = graph.value.addNode({
        id: flowNode.id,
        shape: getNodeShape(nodeType.id),
        x: position.x,
        y: position.y,
        width: flowNode.size.width,
        height: flowNode.size.height,
        label: flowNode.label,
        data: flowNode.data,
        attrs: {
          body: {
            fill: nodeType.color,
            stroke: "#333",
            strokeWidth: 2,
            ...flowNode.style,
          },
        },
      });

      // 节点创建成功
    }

    return flowNode;
  };

  /**
   * 根据节点类型获取形状
   */
  const getNodeShape = (nodeType: string): string => {
    switch (nodeType) {
      case "start":
      case "end":
      case "timer":
        return "flow-circle";
      case "decision":
        return "flow-polygon";
      default:
        return "flow-rect";
    }
  };

  /**
   * 从表格行拖拽创建节点
   */
  const handleTableRowDrag = (rowData: any, event: DragEvent) => {
    // 根据行数据确定节点类型
    const nodeType = determineNodeTypeFromRow(rowData);

    const dragData: DragData = {
      type: "node",
      nodeType: nodeType,
      data: {
        ...rowData,
        sourceType: "table",
      },
      source: rowData,
    };

    handleDragStart(dragData, event);
  };

  /**
   * 根据表格行数据确定节点类型
   */
  const determineNodeTypeFromRow = (rowData: any): string => {
    // 这里可以根据行数据的特征来判断应该创建什么类型的节点
    // 例如根据某个字段的值来决定

    if (rowData.type) {
      return rowData.type;
    }

    if (rowData.category === "start") {
      return "start";
    }

    if (rowData.category === "end") {
      return "end";
    }

    if (rowData.category === "decision") {
      return "decision";
    }

    // 默认返回处理节点
    return "process";
  };

  /**
   * 批量拖拽创建节点
   */
  const handleBatchDrop = (items: any[], startPosition: FlowPosition) => {
    const nodes = [];
    let currentX = startPosition.x;
    let currentY = startPosition.y;
    const spacing = 150;

    for (const item of items) {
      const nodeType = determineNodeTypeFromRow(item);
      const position = { x: currentX, y: currentY };

      const node = createNodeAtPosition(nodeType, position, item);
      if (node) {
        nodes.push(node);
      }

      // 更新下一个节点的位置
      currentX += spacing;
      if (currentX > startPosition.x + spacing * 3) {
        currentX = startPosition.x;
        currentY += 100;
      }
    }

    return nodes;
  };

  /**
   * 设置拖拽区域
   */
  const setupDropZone = (element: HTMLElement) => {
    element.addEventListener("dragenter", handleDragEnter);
    element.addEventListener("dragover", handleDragOver);
    element.addEventListener("dragleave", handleDragLeave);
    element.addEventListener("drop", handleDrop);

    // 返回清理函数
    return () => {
      element.removeEventListener("dragenter", handleDragEnter);
      element.removeEventListener("dragover", handleDragOver);
      element.removeEventListener("dragleave", handleDragLeave);
      element.removeEventListener("drop", handleDrop);
    };
  };

  return {
    isDragging,
    dragData,
    handleDragStart,
    handleTableRowDrag,
    handleBatchDrop,
    setupDropZone,
    createNodeAtPosition,
  };
}
