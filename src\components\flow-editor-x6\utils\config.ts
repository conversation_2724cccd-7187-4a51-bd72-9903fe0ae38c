import type { FlowConfig } from '../types'

/**
 * 创建默认配置
 */
export function createDefaultConfig(): FlowConfig {
  return {
    grid: {
      enabled: true,
      size: 20,
      visible: true
    },
    snap: {
      enabled: true,
      tolerance: 10
    },
    zoom: {
      min: 0.1,
      max: 3,
      step: 0.1
    },
    selection: {
      enabled: true,
      multiple: true
    },
    connection: {
      allowBlank: false,
      allowMulti: false,
      allowLoop: false
    },
    validation: {
      enabled: true,
      realtime: true,
      rules: []
    }
  }
}

/**
 * 合并配置
 */
export function mergeConfig(defaultConfig: FlowConfig, userConfig: Partial<FlowConfig>): FlowConfig {
  return {
    grid: { ...defaultConfig.grid, ...userConfig.grid },
    snap: { ...defaultConfig.snap, ...userConfig.snap },
    zoom: { ...defaultConfig.zoom, ...userConfig.zoom },
    selection: { ...defaultConfig.selection, ...userConfig.selection },
    connection: { ...defaultConfig.connection, ...userConfig.connection },
    validation: { ...defaultConfig.validation, ...userConfig.validation }
  }
}

/**
 * 验证配置
 */
export function validateConfig(config: FlowConfig): string[] {
  const errors: string[] = []

  // 验证网格配置
  if (config.grid.size <= 0) {
    errors.push('Grid size must be greater than 0')
  }

  // 验证缩放配置
  if (config.zoom.min <= 0) {
    errors.push('Zoom min must be greater than 0')
  }
  if (config.zoom.max <= config.zoom.min) {
    errors.push('Zoom max must be greater than zoom min')
  }
  if (config.zoom.step <= 0) {
    errors.push('Zoom step must be greater than 0')
  }

  // 验证对齐配置
  if (config.snap.tolerance < 0) {
    errors.push('Snap tolerance must be non-negative')
  }

  return errors
}
