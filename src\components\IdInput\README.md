# IdInput 组件

基于 PrimeVue InputText 封装的 ID 输入框组件，专门用于输入标识符类型的字符串。

## 功能特性

- ✅ **字符限制**：只允许输入字母、数字、下划线(_)、横线(-)
- ✅ **自动转换**：小写字母自动转换为大写
- ✅ **智能处理**：自动处理所有输入方式（键盘输入、粘贴、中文输入法等）
- ✅ **完全转发**：使用 useAttrs 和 useSlots 转发所有属性、事件和插槽
- ✅ **TypeScript 支持**：完整的类型定义
- ✅ **Vue 3 Composition API**：使用 setup 语法糖
- ✅ **简洁实现**：仅监听 change 事件，使用正则表达式过滤
- ✅ **Tailwind CSS 兼容**：支持 Tailwind 样式类

## 安装依赖

确保项目中已安装以下依赖：

```bash
npm install primevue
```

## 基础用法

```vue
<template>
  <IdInput
    v-model="value"
    placeholder="请输入ID"
    class="w-full"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import IdInput from '@/components/IdInput/index.vue'

const value = ref('')
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `''` | 输入框的值 |
| `maxLength` | `number` | `undefined` | 最大输入长度 |
| `placeholder` | `string` | `''` | 占位符文本 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `readonly` | `boolean` | `false` | 是否只读 |
| `invalid` | `boolean` | `false` | 是否显示错误状态 |
| `variant` | `'filled' \| 'outlined'` | `'outlined'` | 输入框变体 |
| `size` | `'small' \| 'large'` | `undefined` | 输入框尺寸 |

> **注意**：组件还支持 PrimeVue InputText 的所有其他属性，通过 `v-bind="$attrs"` 自动转发。

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: string)` | 值更新时触发 |
| `input` | `(event: Event)` | 输入时触发 |
| `change` | `(event: Event)` | 值改变时触发 |
| `focus` | `(event: FocusEvent)` | 获得焦点时触发 |
| `blur` | `(event: FocusEvent)` | 失去焦点时触发 |
| `keydown` | `(event: KeyboardEvent)` | 按键按下时触发 |
| `keyup` | `(event: KeyboardEvent)` | 按键释放时触发 |
| `keypress` | `(event: KeyboardEvent)` | 按键按下时触发 |

## 方法

通过 `ref` 可以调用以下方法：

```vue
<template>
  <IdInput ref="inputRef" v-model="value" />
  <button @click="focusInput">聚焦</button>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const inputRef = ref()
const value = ref('')

const focusInput = () => {
  inputRef.value?.focus()
}
</script>
```

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getInputElement()` | - | `HTMLInputElement \| null` | 获取输入框DOM元素 |
| `focus()` | - | `void` | 聚焦输入框 |
| `blur()` | - | `void` | 失焦输入框 |
| `select()` | - | `void` | 选中所有文本 |
| `setSelectionRange(start, end)` | `start: number, end: number` | `void` | 设置光标位置 |

## 使用示例

### 基础示例

```vue
<template>
  <div class="space-y-4">
    <!-- 基础用法 -->
    <IdInput
      v-model="userId"
      placeholder="请输入用户ID"
      class="w-full"
    />
    
    <!-- 带长度限制 -->
    <IdInput
      v-model="code"
      placeholder="请输入代码（最多10位）"
      :maxLength="10"
      class="w-full"
    />
    
    <!-- 不同状态 -->
    <IdInput
      v-model="disabledValue"
      disabled
      class="w-full"
    />
    
    <IdInput
      v-model="readonlyValue"
      readonly
      class="w-full"
    />
    
    <IdInput
      v-model="invalidValue"
      invalid
      placeholder="错误状态"
      class="w-full"
    />
  </div>
</template>
```

### 表单集成

```vue
<template>
  <form @submit.prevent="submitForm">
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium mb-2">
          用户ID *
        </label>
        <IdInput
          v-model="form.userId"
          placeholder="请输入用户ID"
          :invalid="!form.userId && submitted"
          required
          class="w-full"
        />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-2">
          项目代码
        </label>
        <IdInput
          v-model="form.projectCode"
          placeholder="请输入项目代码"
          :maxLength="20"
          class="w-full"
        />
      </div>
    </div>
    
    <button type="submit" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded">
      提交
    </button>
  </form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const form = reactive({
  userId: '',
  projectCode: ''
})

const submitted = ref(false)

const submitForm = () => {
  submitted.value = true
  if (form.userId) {
    console.log('提交表单:', form)
  }
}
</script>
```

### 事件监听

```vue
<template>
  <IdInput
    v-model="value"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
    @keydown="onKeydown"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const value = ref('')

const onInput = (event: Event) => {
  console.log('输入:', (event.target as HTMLInputElement).value)
}

const onFocus = () => {
  console.log('获得焦点')
}

const onBlur = () => {
  console.log('失去焦点')
}

const onKeydown = (event: KeyboardEvent) => {
  console.log('按键:', event.key)
}
</script>
```

## 输入规则

| 输入内容 | 输出结果 | 说明 |
|----------|----------|------|
| `hello123` | `HELLO123` | 小写字母转大写 |
| `test_id-01` | `TEST_ID-01` | 允许下划线和横线 |
| `abc@#$123` | `ABC123` | 过滤特殊字符 |
| `user-name_01` | `USER-NAME_01` | 保留允许的字符 |

## 样式定制

组件使用了 scoped 样式，可以通过以下方式自定义：

```vue
<template>
  <IdInput
    v-model="value"
    class="custom-id-input"
  />
</template>

<style>
.custom-id-input {
  font-family: 'JetBrains Mono', monospace;
  font-size: 14px;
  letter-spacing: 1px;
}
</style>
```

## 实现原理

组件采用简洁的实现方式：

1. **监听 change 事件**：只在用户完成输入时进行处理
2. **正则表达式过滤**：使用 `/[^a-zA-Z0-9_-]/g` 过滤无效字符
3. **自动大写转换**：使用 `.toUpperCase()` 转换所有字母
4. **useAttrs/useSlots**：完全转发所有属性和插槽

## 注意事项

1. **字符过滤**：组件在 change 事件时自动过滤不允许的字符
2. **大小写转换**：所有字母都会自动转换为大写
3. **输入方式**：支持键盘输入、粘贴、中文输入法等所有输入方式
4. **事件转发**：所有 PrimeVue InputText 的事件都会正常触发
5. **属性继承**：未在 props 中定义的属性会自动传递给 InputText

## 兼容性

- Vue 3.x
- PrimeVue 3.x
- TypeScript 4.x+
- 现代浏览器（支持 ES6+）
