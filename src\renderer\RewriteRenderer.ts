// 实现BpmnRender的重写，以实现自定义渲染逻辑 满足自定义节点渲染、自定义连线渲染、自定义事件处理等需求
import BaseRenderer from 'diagram-js/lib/draw/BaseRenderer'
import bpmnRenderer from 'bpmn-js/lib/draw/BpmnRenderer'
import { getRectPath } from 'bpmn-js/lib/draw/BpmnRenderUtil'
import type EventBus from 'diagram-js/lib/core/EventBus'
import { ElementLike, ShapeLike } from 'diagram-js/lib/model/Types'
import { NodeTypes, config as renderConfig, isCustomNode } from '@/renderer/config'

import {
  append as svgAppend,
  create as svgCreate,
  attr as svgAttr,
} from 'tiny-svg'

const HIGH_PRIORITY = 1500

export default class CustomRenderer extends BaseRenderer {
  bpmnRenderer: bpmnRenderer
  static $inject: string[];

  constructor(eventBus: EventBus, bpmnRenderer: bpmnRenderer) {
    super(eventBus, HIGH_PRIORITY)
    this.bpmnRenderer = bpmnRenderer
    // 自定义渲染逻辑
  }
  canRender(element: ElementLike): boolean {
    return !element.labelTarget
  }
  /**
   * todo:
   * 1.元素拖动时，会有虚化的样式，需要优化
   * 2.节点选中时的默认样式，暂时使用css隐藏
   */
  drawShape(parentNode: any, element: any) {
    const nodeType = element.nodeType || element.businessObject?.get('nodeType');
    // 如果是NodeTypes中的类型，则进行自定义渲染
    if (isCustomNode(nodeType)) {
      const customShape = renderCustomNode(nodeType as NodeTypes)
      svgAppend(parentNode, customShape);
      return customShape;
    } else {
      // 否则调用bpmn-js的渲染逻辑
      return this.bpmnRenderer.drawShape(parentNode, element);
    }
  }

}

function renderCustomNode(nodeType: NodeTypes): SVGAElement {
  const nodeConfig = renderConfig[(nodeType as NodeTypes)]
  const shape = svgCreate('g', {
    stroke: 'black',
  });
  const nodeStyle = nodeConfig.style
  // 绘制主矩形
  const mainRect = svgCreate('rect', {
    width: nodeStyle.width,
    height: nodeStyle.height,
    fill: 'white',
  });
  svgAppend(shape, mainRect);
  // 绘制左侧颜色块
  const colorBlock = svgCreate('rect', {
    width: '25',
    height: '50',
    fill: nodeStyle.color,
    stroke: 'black',
  });
  svgAppend(shape, colorBlock);
  // 插入图片
  const image = svgCreate('image', {
    href: nodeStyle.icon,
    width: 40,
    height: 40,
    x: 35,
    y: 5,
  });
  svgAppend(shape, image);
  // 添加文字
  const text = svgCreate('text', {
    x: 130,
    y: 25,
    width: 50,
    height: 50,
    'alignment-baseline': 'middle',
    'text-anchor': 'middle',
  });
  text.textContent = nodeStyle.textContent;
  svgAppend(shape, text);
  // 设置阴影
  shape.style.filter = 'drop-shadow(2px 4px 6px rgba(0,0,0,0.5))';
  return shape as SVGAElement;
}

CustomRenderer.$inject = ['eventBus', 'bpmnRenderer']