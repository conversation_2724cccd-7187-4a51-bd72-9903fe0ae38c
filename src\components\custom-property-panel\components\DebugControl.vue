<template>
  <div class="debug-control">
    <a-space>
      <div v-html="debugSvg" :class="getDebugClass()" @click="runDiagram"></div>
      <a-divider direction="vertical" style="margin: 0px" />
      <div v-html="nextStepSkipSvg" :class="getNextStepClass()" @click="nextResume"></div>
      <div v-html="nextStepSvg" :class="getNextStepClass()" @click="nextStep"></div>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { debugApi } from "@/api";
import useModelerStore from "@/store/modeler";
import { debugSvg, nextStepSkipSvg, nextStepSvg } from './svgStr'


const modelerStore = useModelerStore();

function getDebugClass() {
  if (!modelerStore.isRunning && modelerStore.isDebugMode) return 'icon-active'
  return ''
}

function getNextStepClass() {
  return modelerStore.isRunning ? 'icon-active' : ''
}

async function runDiagram() {
  if (!getDebugClass()) return;
  const runRes = await debugApi.startProcess({
    processDefinitionId: modelerStore.getProcessDefId,
  });
  handleRunDiagramProgress(runRes);
}

async function nextResume() {
  if (!getNextStepClass()) return;
  const runRes = await debugApi.resume(modelerStore.getDataId);
  handleRunDiagramProgress(runRes);
}

async function nextStep() {
  if (!getNextStepClass()) return;
  const runRes = await debugApi.step(modelerStore.getDataId!);
  console.log('%c [ runRes ]-39', 'font-size:13px; background:pink; color:#bf2c9f;', runRes)
  handleRunDiagramProgress(runRes);
}

function handleRunDiagramProgress(runRes: any) {
  // 判断结果是否为对象 如果不是说明运行结束
  if (modelerStore.currentBreakpoint)
    modelerStore.forceRemoveOverlay(modelerStore.currentBreakpoint);
  if (typeof runRes !== "object") {
    // modelerStore.isRunning = false
    modelerStore.activeElementId = "";
    // 获取结束节点
    const elementRegistry: any =
      modelerStore.getModeler!.get("elementRegistry");
    const endElements = elementRegistry.filter(
      (e: any) => e.type === "bpmn:EndEvent"
    );
    const modeling: any = modelerStore.getModeler!.get("modeling");
    modeling.setColor(endElements as any, {
      fill: "green",
    });
    return;
  }
  if (!modelerStore.isRunning) modelerStore.isRunning = true;
  modelerStore.dataId = runRes.id;
  modelerStore.variablesManager[runRes.currentActivityId] =
    runRes.variables;
  modelerStore.currentBreakpoint = runRes.currentActivityId;
  modelerStore.setActiveElement(runRes.currentActivityId);
}
</script>

<style lang="scss">
.debug-control {
  width: 100%;

  .arco-space {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    background-color: var(--color-bg-2);
  }

  .icon-active {
    fill: green;
    cursor: pointer;

    &:hover {
      scale: 1.2;
    }

    &:active {
      scale: 0.9;
    }
  }
}
</style>
