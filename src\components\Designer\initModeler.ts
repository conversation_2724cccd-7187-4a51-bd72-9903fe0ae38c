import { ref, onMounted, nextTick } from "vue";
import BpmnModeler from "bpmn-js/lib/Modeler";
import customRendererModule from "@/renderer";
import camundaModdleDescriptor from "camunda-bpmn-moddle/resources/camunda.json";
import CustomContextPad from "@/context-pad";
import myCustomModdleDescriptor from "@/renderer/custom.json";
import {
  NodeTypes,
  config as renderConfig,
  isCustomNode,
} from "@/renderer/config";
import useModelerStore from "@/store/modeler";
import { usedebugEvent } from "./debug/event-handler";
import "../style/index.scss";
import customRule from "./customRule";

export default function initModeler() {
  const modelerStore = useModelerStore();
  const debugBpmnContainer: any = ref(null);
  const editBpmnContainer: any = ref(null);

  const debugModeler: any = ref(null);
  const editModeler: any = ref(null);
  const publicModules = [customRendererModule];

  // 只读模式配置
  const readonlyMode = {
    paletteProvider: ["value", ""], //禁用/清空左侧工具栏
    labelEditingProvider: ["value", ""], //禁用节点编辑
    contextPadProvider: ["value", ""], //禁用图形菜单
    bendpoints: ["value", {}], //禁用连线拖动
    // zoomScroll: ["value", ''],//禁用滚动
    // moveCanvas:['value',''],//禁用拖动整个流程图
    move: ["value", ""], //禁用单个图形拖动
  };

  /**
   * 初始化debug模式,只读模式,禁用右键菜单,禁用滚动,禁用拖动整个流程图,禁用单个图形拖动
   * 鼠标悬浮到节点上,显示断点图标,点击图标,发送post请求,设置断点，并且显示断点图标
   * 后端获取消息,高亮暂停执行的debug节点,并显示日志信息
   */
  function initEditMode() {
    editModeler.value = new BpmnModeler({
      container: editBpmnContainer.value,
      additionalModules: [
        ...publicModules,
        {
          // labelEditingProvider: ["value", ''],//禁用节点编辑
        },
        CustomContextPad,
        customRule,
      ],
      //moddleExtensions的作用是添加额外的模型 例如camunda的模型 用于扩展bpmn的模型
      moddleExtensions: {
        camunda: camundaModdleDescriptor,
        myExt: myCustomModdleDescriptor,
      },
    });
    // 设置点击事件
    const eventBus = editModeler.value.get("eventBus");
    eventBus.on("element.click", (event: any) => {
      console.log(
        "%c [ event ]-62",
        "font-size:13px; background:pink; color:#bf2c9f;",
        event.element
      );
      /**
       * 使用selection.changed监听选中的元素;
       * 使用element.changed监听发生改变的元素.
       */
      if (event.element.type !== "bpmn:Process") {
        // modelerStore.setElement(event.element)
      }
    });
    eventBus.on("shape.added", function (context: any) {
      // 自定义节点添加时,使用updateProperties, 否则saveXml时 属性会消失
      const element = context.element;
      if (!isCustomNode(element.nodeType)) return;
      const modeling = editModeler.value.get("modeling");
      nextTick(() =>
        modeling.updateProperties(element, {
          nodeType: element.nodeType,
          value: "123456",
        })
      );
    });
  }
  function initDebugMode() {
    // 初始化Modeler
    debugModeler.value = new BpmnModeler({
      container: debugBpmnContainer.value,
      additionalModules: [...publicModules, readonlyMode],
      moddleExtensions: {
        camunda: camundaModdleDescriptor,
        myExt: myCustomModdleDescriptor,
      },
    });
    const eventBus = debugModeler.value.get("eventBus");
    usedebugEvent(eventBus, debugModeler.value);
  }

  async function init() {
    initDebugMode();
    initEditMode();
    modelerStore.setModeler(debugModeler.value, editModeler.value);
    await modelerStore.loadDiagram();
  }
  return {
    debugBpmnContainer,
    editBpmnContainer,
    debugModeler,
    editModeler,
    init,
  };
}
