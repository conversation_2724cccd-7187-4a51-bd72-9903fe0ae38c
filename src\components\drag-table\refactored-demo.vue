<template>
  <div class="refactored-demo">
    <!-- 标题和说明 -->
    <div class="demo-header">
      <h2>重构版拖拽表格演示 - 新算法测试</h2>
      <div class="instructions">
        <h3>新算法特性：</h3>
        <ul>
          <li><strong>忽略禁用行：</strong>只对启用行进行重排序</li>
          <li><strong>位置保持：</strong>禁用行始终保持在原位置</li>
          <li>
            <strong>简化逻辑：</strong>先提取启用项目，重排序后再插入禁用项目
          </li>
          <li><strong>Hook 架构：</strong>拖拽逻辑完全分离到独立的 hook 中</li>
        </ul>

        <h3>测试场景：</h3>
        <div class="test-scenarios">
          <div class="scenario">
            <strong>场景1：</strong>将"项目1"拖拽到"项目3(禁用)"上 →
            应该与项目2交换位置
          </div>
          <div class="scenario">
            <strong>场景2：</strong>将"项目5"拖拽到"项目6"上 → 应该正常交换
          </div>
          <div class="scenario">
            <strong>场景3：</strong>将"项目2"拖拽到"项目4(禁用)"下方 →
            应该移动到项目5前面
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <button @click="resetData" class="btn btn-primary">重置数据</button>
      <button @click="toggleDebug" class="btn btn-secondary">
        {{ debugMode ? "关闭" : "开启" }}调试模式
      </button>
      <button @click="addRandomItem" class="btn btn-success">
        添加随机项目
      </button>
      <button @click="shuffleEnabled" class="btn btn-info">
        随机打乱启用项目
      </button>
    </div>

    <!-- 重构版拖拽表格 -->
    <RefactoredDragTable
      v-model:data="tableData"
      :columns="tableColumns"
      :debug="debugMode"
      key-field="id"
      display-field="name"
      disabled-field="disabled"
      @row-reorder="handleRowReorder"
    >
      <!-- 状态列模板 -->
      <template #status="{ data }">
        <span
          class="status-badge"
          :class="data.disabled ? 'status-disabled' : 'status-enabled'"
        >
          {{ data.disabled ? "禁用" : "启用" }}
        </span>
      </template>

      <!-- 操作列模板 -->
      <template #actions="{ data, index }">
        <div class="action-buttons">
          <button
            @click="toggleItemStatus(index)"
            class="btn btn-sm"
            :class="data.disabled ? 'btn-success' : 'btn-warning'"
          >
            {{ data.disabled ? "启用" : "禁用" }}
          </button>
          <button @click="deleteItem(index)" class="btn btn-sm btn-danger">
            删除
          </button>
        </div>
      </template>
    </RefactoredDragTable>

    <!-- 算法对比 -->
    <div class="algorithm-comparison">
      <h3>算法对比：</h3>
      <div class="comparison-grid">
        <div class="algorithm-card">
          <h4>旧算法</h4>
          <ul>
            <li>复杂的位置计算</li>
            <li>需要处理禁用行的特殊情况</li>
            <li>多种边界条件判断</li>
            <li>代码逻辑复杂</li>
          </ul>
        </div>
        <div class="algorithm-card new">
          <h4>新算法</h4>
          <ul>
            <li>简单的数组操作</li>
            <li>忽略禁用行，只处理启用项目</li>
            <li>逻辑清晰，易于理解</li>
            <li>代码维护性强</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 数据状态显示 -->
    <div class="data-display">
      <h3>当前数据状态：</h3>
      <div class="data-list">
        <div
          v-for="(item, index) in tableData"
          :key="item.id"
          class="data-item"
          :class="{ disabled: item.disabled }"
        >
          <span class="item-index">{{ index + 1 }}.</span>
          <span class="item-name">{{ item.name }}</span>
          <span class="item-status"
            >({{ item.disabled ? "禁用" : "启用" }})</span
          >
        </div>
      </div>
    </div>

    <!-- 操作历史 -->
    <div class="operation-history" v-if="operationHistory.length > 0">
      <h3>操作历史：</h3>
      <div class="history-list">
        <div
          v-for="(operation, index) in operationHistory.slice(-5)"
          :key="index"
          class="history-item"
        >
          <span class="history-time">{{ operation.time }}</span>
          <span class="history-action">{{ operation.action }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import RefactoredDragTable from "./refactored-drag-table.vue";

// ==================== 数据定义 ====================
interface TableItem {
  id: number;
  name: string;
  description: string;
  disabled: boolean;
  priority: string;
}

interface OperationRecord {
  time: string;
  action: string;
}

// ==================== 响应式数据 ====================
const debugMode = ref(false);
const nextId = ref(7);

const tableData = ref<TableItem[]>([
  {
    id: 1,
    name: "项目1",
    description: "第一个项目",
    disabled: false,
    priority: "高",
  },
  {
    id: 2,
    name: "项目2",
    description: "第二个项目",
    disabled: false,
    priority: "中",
  },
  {
    id: 3,
    name: "项目3",
    description: "第三个项目（禁用）",
    disabled: true,
    priority: "低",
  },
  {
    id: 4,
    name: "项目4",
    description: "第四个项目（禁用）",
    disabled: true,
    priority: "中",
  },
  {
    id: 5,
    name: "项目5",
    description: "第五个项目",
    disabled: false,
    priority: "高",
  },
  {
    id: 6,
    name: "项目6",
    description: "第六个项目",
    disabled: false,
    priority: "低",
  },
]);

const tableColumns = [
  { field: "name", header: "项目名称", style: { minWidth: "150px" } },
  { field: "description", header: "描述", style: { minWidth: "200px" } },
  { field: "priority", header: "优先级", style: { width: "100px" } },
  {
    field: "status",
    header: "状态",
    template: "status",
    style: { width: "100px" },
  },
  {
    field: "actions",
    header: "操作",
    template: "actions",
    style: { width: "150px" },
  },
];

const operationHistory = ref<OperationRecord[]>([]);

// ==================== 工具函数 ====================
const addOperationRecord = (action: string) => {
  operationHistory.value.push({
    time: new Date().toLocaleTimeString(),
    action,
  });
};

const resetData = () => {
  tableData.value = [
    {
      id: 1,
      name: "项目1",
      description: "第一个项目",
      disabled: false,
      priority: "高",
    },
    {
      id: 2,
      name: "项目2",
      description: "第二个项目",
      disabled: false,
      priority: "中",
    },
    {
      id: 3,
      name: "项目3",
      description: "第三个项目（禁用）",
      disabled: true,
      priority: "低",
    },
    {
      id: 4,
      name: "项目4",
      description: "第四个项目（禁用）",
      disabled: true,
      priority: "中",
    },
    {
      id: 5,
      name: "项目5",
      description: "第五个项目",
      disabled: false,
      priority: "高",
    },
    {
      id: 6,
      name: "项目6",
      description: "第六个项目",
      disabled: false,
      priority: "低",
    },
  ];
  nextId.value = 7;
  operationHistory.value = [];
  addOperationRecord("重置数据到初始状态");
};

const toggleDebug = () => {
  debugMode.value = !debugMode.value;
  addOperationRecord(`${debugMode.value ? "开启" : "关闭"}调试模式`);
};

const addRandomItem = () => {
  const priorities = ["高", "中", "低"];
  const randomPriority =
    priorities[Math.floor(Math.random() * priorities.length)];

  const newItem: TableItem = {
    id: nextId.value++,
    name: `项目${nextId.value - 1}`,
    description: `随机添加的项目`,
    disabled: Math.random() > 0.7, // 30% 概率禁用
    priority: randomPriority,
  };

  tableData.value.push(newItem);
  addOperationRecord(`添加新项目: ${newItem.name}`);
};

const shuffleEnabled = () => {
  // 只打乱启用的项目，保持禁用项目位置不变
  const enabledItems = tableData.value.filter((item) => !item.disabled);
  const disabledPositions = new Map<number, TableItem>();

  // 记录禁用项目的位置
  tableData.value.forEach((item, index) => {
    if (item.disabled) {
      disabledPositions.set(index, item);
    }
  });

  // 打乱启用项目
  for (let i = enabledItems.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [enabledItems[i], enabledItems[j]] = [enabledItems[j], enabledItems[i]];
  }

  // 重建数组
  const newData: TableItem[] = [];
  let enabledIndex = 0;

  for (let i = 0; i < tableData.value.length; i++) {
    if (disabledPositions.has(i)) {
      newData.push(disabledPositions.get(i)!);
    } else {
      newData.push(enabledItems[enabledIndex++]);
    }
  }

  tableData.value = newData;
  addOperationRecord("随机打乱启用项目顺序");
};

const toggleItemStatus = (index: number) => {
  const item = tableData.value[index];
  item.disabled = !item.disabled;
  addOperationRecord(
    `切换 ${item.name} 状态为 ${item.disabled ? "禁用" : "启用"}`
  );
};

const deleteItem = (index: number) => {
  const item = tableData.value[index];
  tableData.value.splice(index, 1);
  addOperationRecord(`删除项目: ${item.name}`);
};

// ==================== 事件处理 ====================
const handleRowReorder = (event: { originalEvent: Event; value: any[] }) => {
  const oldOrder = tableData.value.map((item) => item.name).join(", ");
  const newOrder = event.value.map((item) => item.name).join(", ");

  addOperationRecord(`拖拽重排序: [${oldOrder}] → [${newOrder}]`);

  console.log("拖拽重排序事件:", {
    oldOrder,
    newOrder,
    data: event.value,
  });
};
</script>

<style scoped>
/* ==================== 演示页面样式 ==================== */
.refactored-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* ==================== 头部样式 ==================== */
.demo-header {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.demo-header h2 {
  margin: 0 0 15px 0;
  font-size: 28px;
  font-weight: 700;
}

.instructions {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
}

.instructions h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #f8f9fa;
}

.instructions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.instructions li {
  margin: 8px 0;
  line-height: 1.5;
}

.test-scenarios {
  margin-top: 15px;
}

.scenario {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  margin: 8px 0;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
}

/* ==================== 控制面板样式 ==================== */
.control-panel {
  display: flex;
  gap: 12px;
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #117a8b;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* ==================== 算法对比样式 ==================== */
.algorithm-comparison {
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.algorithm-comparison h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.algorithm-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #dc3545;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.algorithm-card.new {
  border-left-color: #28a745;
}

.algorithm-card h4 {
  margin: 0 0 15px 0;
  color: #495057;
}

.algorithm-card ul {
  margin: 0;
  padding-left: 20px;
}

.algorithm-card li {
  margin: 8px 0;
  line-height: 1.4;
}

/* ==================== 状态徽章样式 ==================== */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 50px;
}

.status-enabled {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-disabled {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* ==================== 操作按钮样式 ==================== */
.action-buttons {
  display: flex;
  gap: 6px;
}

/* ==================== 数据显示样式 ==================== */
.data-display {
  margin: 30px 0;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.data-display h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.data-item.disabled {
  border-left-color: #dc3545;
  opacity: 0.7;
}

.item-index {
  font-weight: 600;
  color: #6c757d;
  margin-right: 8px;
  min-width: 25px;
}

.item-name {
  font-weight: 500;
  color: #495057;
  margin-right: 8px;
}

.item-status {
  color: #6c757d;
  font-size: 14px;
}

/* ==================== 操作历史样式 ==================== */
.operation-history {
  margin: 30px 0;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.operation-history h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.history-time {
  color: #6c757d;
  margin-right: 12px;
  font-family: monospace;
  min-width: 80px;
}

.history-action {
  color: #495057;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .control-panel {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
