/**
 * 流程编辑器常量配置
 */

// 默认尺寸
export const DEFAULT_SIZES = {
  NODE: {
    width: 120,
    height: 60
  },
  CIRCLE_NODE: {
    width: 80,
    height: 80
  },
  DECISION_NODE: {
    width: 100,
    height: 80
  }
} as const

// 颜色配置
export const COLORS = {
  PRIMARY: '#5F95FF',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  DANGER: '#F44336',
  INFO: '#2196F3',
  SECONDARY: '#9C27B0',
  
  // 端口颜色
  INPUT_PORT: '#31d0c6',
  OUTPUT_PORT: '#fe854f',
  
  // 连线颜色
  EDGE_DEFAULT: '#A2B1C3',
  EDGE_SELECTED: '#5F95FF',
  
  // 背景色
  CANVAS_BG: '#FAFAFA',
  PANEL_BG: '#F5F5F5'
} as const

// 网格配置
export const GRID_CONFIG = {
  SIZE: 20,
  VISIBLE: true,
  TYPE: 'doubleMesh',
  ARGS: [
    { color: '#E7E8EA', thickness: 1 },
    { color: '#CBCED3', thickness: 1, factor: 4 }
  ]
} as const

// 端口配置
export const PORT_CONFIG = {
  RADIUS: 6,
  STROKE_WIDTH: 2,
  VISIBILITY: 'hidden'
} as const

// 连线配置
export const EDGE_CONFIG = {
  ROUTER: 'manhattan',
  CONNECTOR: {
    name: 'rounded',
    args: { radius: 8 }
  },
  STROKE_WIDTH: 2,
  MARKER: {
    name: 'block',
    width: 12,
    height: 8
  }
} as const

// 缩放配置
export const ZOOM_CONFIG = {
  MIN_SCALE: 0.1,
  MAX_SCALE: 3,
  STEP: 0.1,
  FIT_PADDING: 20
} as const

// 键盘快捷键
export const KEYBOARD_SHORTCUTS = {
  UNDO: ['ctrl+z', 'cmd+z'],
  REDO: ['ctrl+y', 'cmd+y', 'ctrl+shift+z', 'cmd+shift+z'],
  COPY: ['ctrl+c', 'cmd+c'],
  PASTE: ['ctrl+v', 'cmd+v'],
  DELETE: ['delete', 'backspace'],
  SELECT_ALL: ['ctrl+a', 'cmd+a']
} as const

// 节点类别
export const NODE_CATEGORIES = {
  CONTROL: 'control',
  PROCESS: 'process',
  DATA: 'data',
  EVENT: 'event',
  INTEGRATION: 'integration'
} as const

// 验证状态
export const VALIDATION_STATES = {
  VALID: 'valid',
  INVALID: 'invalid',
  WARNING: 'warning',
  PENDING: 'pending'
} as const

// 验证类型
export const VALIDATION_TYPES = {
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
} as const

// 拖拽类型
export const DRAG_TYPES = {
  NODE: 'node',
  EDGE: 'edge'
} as const

// 历史记录操作类型
export const HISTORY_ACTIONS = {
  ADD: 'add',
  UPDATE: 'update',
  DELETE: 'delete',
  MOVE: 'move'
} as const

// 导出格式
export const EXPORT_FORMATS = {
  JSON: 'json',
  XML: 'xml',
  SVG: 'svg',
  PNG: 'png'
} as const

// 事件名称
export const EVENTS = {
  NODE_ADDED: 'node:added',
  NODE_REMOVED: 'node:removed',
  NODE_UPDATED: 'node:updated',
  NODE_SELECTED: 'node:selected',
  NODE_DESELECTED: 'node:deselected',
  EDGE_ADDED: 'edge:added',
  EDGE_REMOVED: 'edge:removed',
  EDGE_UPDATED: 'edge:updated',
  EDGE_SELECTED: 'edge:selected',
  EDGE_DESELECTED: 'edge:deselected',
  VALIDATION_CHANGED: 'validation:changed',
  DATA_CHANGED: 'data:changed'
} as const

// 工具栏按钮配置
export const TOOLBAR_BUTTONS = {
  UNDO: {
    icon: 'pi pi-undo',
    tooltip: '撤销 (Ctrl+Z)',
    shortcut: 'ctrl+z'
  },
  REDO: {
    icon: 'pi pi-refresh',
    tooltip: '重做 (Ctrl+Y)',
    shortcut: 'ctrl+y'
  },
  ZOOM_IN: {
    icon: 'pi pi-plus',
    tooltip: '放大'
  },
  ZOOM_OUT: {
    icon: 'pi pi-minus',
    tooltip: '缩小'
  },
  ZOOM_FIT: {
    icon: 'pi pi-expand',
    tooltip: '适应画布'
  },
  ZOOM_ACTUAL: {
    icon: 'pi pi-search',
    tooltip: '实际大小'
  },
  COPY: {
    icon: 'pi pi-copy',
    tooltip: '复制 (Ctrl+C)',
    shortcut: 'ctrl+c'
  },
  PASTE: {
    icon: 'pi pi-clone',
    tooltip: '粘贴 (Ctrl+V)',
    shortcut: 'ctrl+v'
  },
  DELETE: {
    icon: 'pi pi-trash',
    tooltip: '删除 (Delete)',
    shortcut: 'delete'
  }
} as const

// 错误消息
export const ERROR_MESSAGES = {
  CONTAINER_REQUIRED: 'Container element is required',
  GRAPH_NOT_INITIALIZED: 'Graph is not initialized',
  NODE_NOT_FOUND: 'Node not found',
  EDGE_NOT_FOUND: 'Edge not found',
  INVALID_NODE_TYPE: 'Invalid node type',
  INVALID_CONNECTION: 'Invalid connection',
  VALIDATION_FAILED: 'Validation failed'
} as const
