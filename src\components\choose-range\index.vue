<template>
  <div class="choose-range" @mousemove="handleDrag($event)" @mouseup="endDrag" ref="container"
    :style="containerBackground">
    <div class="left-anchor anchor" @mousedown="startDrag('left')" ref="leftAnchor"></div>
    <div class="right-anchor anchor" @mousedown="startDrag('right')" ref="rightAnchor"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref, computed } from 'vue'

const mousedownFlag = ref(false)
const targetEl = ref('')
const leftAnchor: Ref<HTMLDivElement | null> = ref(null)
const rightAnchor: Ref<HTMLDivElement | null> = ref(null)
const container: Ref<HTMLDivElement | null> = ref(null)

const size = 30

const containerBackground = computed(() => {
  if (!container.value) {
    return ''
  }
  console.log('%c [ container.value ]-22', 'font-size:13px; background:pink; color:#bf2c9f;', container.value)

  // 根据size计算背景刻度
  return `background-image: repeating-linear-gradient(90deg, blue 0, blue 2px, transparent 0, transparent ${container.value!.offsetWidth / size}px)`
})

function startDrag(target: string) {
  mousedownFlag.value = true
  targetEl.value = target
}
function endDrag(e: MouseEvent) {
  mousedownFlag.value = false
}
function handleDrag(e: MouseEvent) {
  if (!mousedownFlag.value) return
  let position = e.clientX - container.value!.offsetLeft
  console.log('%c [ position ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', position)
  if (targetEl.value === 'left') {
    // 不能超过右边anchor的位置
    if (position > rightAnchor.value!.offsetLeft - 2) {
      position = rightAnchor.value!.offsetLeft - 2
    }
    leftAnchor.value!.style.left = position + 'px'
  }
  if (targetEl.value === 'right') {
    // 不能超过左边anchor的位置
    if (position < leftAnchor.value!.offsetLeft + 2) {
      position = leftAnchor.value!.offsetLeft + 2
    }
    rightAnchor.value!.style.left = position + 'px'
  }
}

</script>

<style lang="scss">
.choose-range {
  width: 100%;
  height: 100px;
  background-color: gray;
  position: relative;
  // background-image: repeating-linear-gradient(90deg, blue 0, blue 2px, transparent 0, transparent 100px);

  .anchor {
    height: 100%;
    width: 2px;
    background-color: red;
    position: absolute;
    cursor: move;
  }

  .left-anchor {
    left: 10px;
  }

  .right-anchor {
    right: 10px;
  }
}
</style>