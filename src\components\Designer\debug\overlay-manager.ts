import './debug.scss'
import { debugApi, changeSessionId } from '@/api'
import useModelerStore from '@/store/modeler'
import { remove as removeArrayElement } from 'lodash'

export function useOverlayManager(modeler: any) {
  const modelerStore = useModelerStore()
  const overlays = modeler.get('overlays')

  function addOverlay(element: any, type: string, options?: any) {
    if (modelerStore.overlayerManager[element.id]) {
      modelerStore.overlayerManager[element.id].isHover++
      return
    }
    const { htmlId, html } = getHtmlStr(element.id, type)
    const overlayId = overlays.add(element, type, {
      html,
      position: {
        bottom: -5,
        left: 0
      },
      ...options
    })
    modelerStore.overlayerManager[element.id] = {
      overlayId,
      isDebugNode: false,
      isHover: 1,
      variables: null
    }
    // 添加overlay元素的点击事件
    const overlayHtml = document.getElementById(htmlId)
    bindOverlayEvents(overlayHtml as HTMLElement, element)
  }

  function removeOverlay(element: any) {
    const overlayElenment = modelerStore.overlayerManager[element.id];
    if (!overlayElenment || overlayElenment.isDebugNode || overlayElenment.isHover > 0) return;
    overlays.remove(overlayElenment.overlayId);
    modelerStore.overlayerManager[element.id] = null;
  }

  function bindOverlayEvents(overlayHtml: HTMLElement, element: any) {
    overlayHtml!.addEventListener('click', async () => {
      if (modelerStore.isRunning) return
      if (modelerStore.overlayerManager[element.id].isDebugNode) {
        removeArrayElement(modelerStore.breakpoints, (item: any) => item.elementId === element.id)
        await debugApi.setBreakPoints({
          breakpoints: modelerStore.breakpoints
        })
        modelerStore.overlayerManager[element.id].isDebugNode = false
        overlayHtml!.classList.remove('pause-node')
      } else {
        modelerStore.breakpoints.push({
          elementId: element.id,
          businessObject: element.businessObject,
          processDefinitionId: modelerStore.getProcessDefId,
          type: "BEFORE_ACTIVITY",
          condition: {
            language: element.businessObject?.get('scriptFormat'),
          }
        })
        await debugApi.setBreakPoints({
          breakpoints: modelerStore.breakpoints
        })
        modelerStore.overlayerManager[element.id].isDebugNode = true
        overlayHtml!.classList.add('pause-node')
      }
    })
    // 添加overlay元素的鼠标移入事件
    overlayHtml!.addEventListener('mouseover', () => {
      // console.log('overlay mouseover')
      modelerStore.overlayerManager[element.id].isHover++
    })
    // 添加overlay元素的鼠标移出事件
    overlayHtml!.addEventListener('mouseout', () => {
      // console.log('overlay mouseout')
      modelerStore.overlayerManager[element.id].isHover--
      setTimeout(() => {
        removeOverlay(element);
      }, 200);
    })
  }
  return {
    addOverlay,
    removeOverlay,
  }
}

function getHtmlStr(elementId: string, type: string) {
  const htmlId = `overlay-${type}-${elementId}`
  return {
    html:
      `<svg id="${htmlId}" class="bpmn-overlay ${type} arco-icon" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter" style="font-size: 24px;">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm-6-27a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1V18a1 1 0 0 0-1-1h-3Zm9 0a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1V18a1 1 0 0 0-1-1h-3Z" fill="currentColor" stroke="none"></path>
      </svg>`,
    htmlId
  }
}

export default useOverlayManager;