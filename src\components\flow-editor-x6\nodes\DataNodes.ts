import { BaseNode } from './BaseNode'

/**
 * 数据库节点
 */
export class DatabaseNode extends BaseNode {
  constructor() {
    super({
      id: 'database',
      name: '数据库',
      category: 'data',
      icon: 'pi pi-database',
      color: '#3F51B5',
      description: '数据库操作节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      operation: 'select', // select, insert, update, delete
      connection: {
        type: 'mysql', // mysql, postgresql, mongodb, etc.
        host: '',
        port: 3306,
        database: '',
        username: '',
        password: ''
      },
      query: '',
      parameters: {},
      timeout: 30000,
      transaction: false
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#303F9F',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.query || node.data.query.trim() === '') {
      errors.push('数据库节点必须指定查询语句')
    }

    if (!node.data.connection.host) {
      errors.push('数据库节点必须指定主机地址')
    }

    return errors
  }
}

/**
 * 文件节点
 */
export class FileNode extends BaseNode {
  constructor() {
    super({
      id: 'file',
      name: '文件',
      category: 'data',
      icon: 'pi pi-file',
      color: '#607D8B',
      description: '文件操作节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      operation: 'read', // read, write, append, delete, copy, move
      path: '',
      encoding: 'utf8',
      format: 'text', // text, json, csv, xml
      backup: false,
      createDir: true
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#455A64',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.path || node.data.path.trim() === '') {
      errors.push('文件节点必须指定文件路径')
    }

    return errors
  }
}

/**
 * 缓存节点
 */
export class CacheNode extends BaseNode {
  constructor() {
    super({
      id: 'cache',
      name: '缓存',
      category: 'data',
      icon: 'pi pi-server',
      color: '#FF9800',
      description: '缓存操作节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      operation: 'get', // get, set, delete, exists, expire
      key: '',
      value: '',
      ttl: 3600, // 秒
      connection: {
        type: 'redis',
        host: 'localhost',
        port: 6379,
        database: 0
      }
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#F57C00',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.key || node.data.key.trim() === '') {
      errors.push('缓存节点必须指定键名')
    }

    return errors
  }
}
