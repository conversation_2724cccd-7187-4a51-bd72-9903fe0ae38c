import { BaseNode } from './BaseNode'

/**
 * 定时器节点
 */
export class TimerNode extends BaseNode {
  constructor() {
    super({
      id: 'timer',
      name: '定时器',
      category: 'event',
      icon: 'pi pi-clock',
      color: '#795548',
      description: '定时触发节点',
      defaultSize: { width: 100, height: 80 },
      allowedConnections: {
        input: [],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      schedule: {
        type: 'interval', // interval, cron, once
        value: 60000, // 毫秒或cron表达式
        timezone: 'Asia/Shanghai'
      },
      enabled: true,
      maxExecutions: -1 // -1表示无限制
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 50,
      fill: this.color,
      stroke: '#5D4037',
      strokeWidth: 2
    }
  }

  getX6Shape(): string {
    return 'flow-circle'
  }

  protected createPorts() {
    return [
      {
        id: 'right',
        group: 'output' as const,
        position: { x: 0, y: 0 },
        label: '触发'
      }
    ]
  }
}

/**
 * Webhook节点
 */
export class WebhookNode extends BaseNode {
  constructor() {
    super({
      id: 'webhook',
      name: 'Webhook',
      category: 'event',
      icon: 'pi pi-globe',
      color: '#009688',
      description: 'HTTP回调节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      method: 'POST',
      url: '',
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000,
      retryCount: 3,
      authentication: {
        type: 'none', // none, basic, bearer, apikey
        credentials: {}
      }
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#00796B',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.url || node.data.url.trim() === '') {
      errors.push('Webhook节点必须指定URL')
    }

    try {
      new URL(node.data.url)
    } catch {
      errors.push('Webhook URL格式不正确')
    }

    return errors
  }
}

/**
 * 消息节点
 */
export class MessageNode extends BaseNode {
  constructor() {
    super({
      id: 'message',
      name: '消息',
      category: 'event',
      icon: 'pi pi-envelope',
      color: '#FF5722',
      description: '消息事件节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: [],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      messageType: 'queue', // queue, topic, direct
      source: '',
      filter: {},
      timeout: 30000
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#D84315',
      strokeWidth: 2
    }
  }

  protected createPorts() {
    return [
      {
        id: 'right',
        group: 'output' as const,
        position: { x: 0, y: 0 },
        label: '消息'
      }
    ]
  }
}
