<rule name="base" user="admin" version="" directory="" seed value="5" task_count="2" client
  version="v1.0.2#2024020509" server_version="v1.0.2#2022-08-19">
  <using_member list>
    <uDBConnection values="oracle_61 qcubic_71 " />
  </using_member list>
  <rule_comments />
  <max thread _count use import exe list="true" count="3" />
  <db_transaction_mode mode="Autocommit" />
  <max_divide_count count="o" />
  <startable ignore mode="false" />
  <endable ignore mode="false" />
  <execute_rulecall thread mode="false" />
  <execute_oplread thread mode="false" />
  <execute_rtdread thread mode="true" />
  <execute_splunkread_thread mode="false" />
  <use _nvl use integer="false" integer="0" use_double="false" double="0" />
  <rule_description />
  <global variable>
  <variable name="$EXITREASONCODE" type="string" desc=">"" </variable></global variable> 168 <variable name=" $EXITREASONMESSAGE" type=" string" desc="">""</variable><main_process>.<flow>linkchangedflag=" false" isinloop=" false" /><start task id=" 0 0" name=" [START" icon_groupid="" icon="" shape=" 60, 60, 50, 50" active=" true" break_point="" status=" true" applydatetime="" executing_ order=" 0" timeout="" dev_mode=" False"executing_order=" 1" timeout="" dev_mode=" False" linkchangedflag=" false" isinloop=" false"><db_read task id=" 2ID202403201705284297932521_0" name=" db read 2" icon_groupid=" icon=" shape=" 320, 280, 50, 50" active=" true" break_point=" status="true"
    applydatetime="2024-03-22 09:50:23" />
  <desc />
  <database name="oracle_61" using_cache="N" sec="" view_option="false"
    using_separate_db_connection="false" />
  <query_statement edit="True" check_retry="false" retry_count="1" check_row_count="1"
    use_nvl="false">select
    V1 from COwIN_TEST_15 where V1='1'
  </query_statement>
  <recovery_data />
  <output_factor table="db_readby202403201705284294477522" cols="1">
    <column name="V1" type="string" org_type="" ellipsis="false" />
  </output_factor>
  <next_flow>
  <task id="4ID202403211650094393433526_11" condition="1" points="" color="-12822896" />
  </next_ flow>
  </db_read>
  <desc />
  <database name="qcubic_71" />
  <dbwrite type type="insert" truncate="false" />VALUES (IV1] )
  <query_hint>
    <insert />
    <update />
    <delete />
    <merge into />
  </query_hint>VALUES (V1] )

  <temp_query_statement>INSERT INTO COWIN_TEST_WEB(V1 )</temp_query_statement>
  <field name="V1" column="V1" flag="true" type="string" length="40" autocut="false"
    inputmode="column" />
  </field_matches>
  <input factor table="db_readby202403201705284294477522" copy_flag="false" cols="1><column name=" V1" type=" string" ellipsis=" false" /></input_factor><output factor table=" db_writeby202403211650094391483527" cols=" 1"><column name=" V1" type=" string" ellipsis=" false" /></output_factor><read trace gv info variables=" />
  <next flow />
  <history user="admin" action= *Update" date=" 2024-03-22 9:50:23" version-info=" comment="* /><history user=" admin" action=" Save as" date=*2024-03-17 1:06:20" version info="" comment= *>No Title 0 -&gt;
    base
  </history>
</rule>