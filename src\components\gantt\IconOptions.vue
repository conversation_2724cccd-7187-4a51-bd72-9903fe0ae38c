<template>
  <svg width="12" height="12" viewBox="0 0 12 12" fill="#909294" xmlns="http://www.w3.org/2000/svg">
    <g id="nav_tab_link">
      <rect id="Rectangle 250" x="2.3916" y="10.0119" width="0.5" height="10.3114" rx="0.25"
        transform="rotate(-135 2.3916 10.0119)" stroke-width="0.5" />
      <path id="Rectangle 398" d="M3 2.5C3 2.22386 3.22386 2 3.5 2H10V3H3.5C3.22386 3 3 2.77614 3 2.5Z" />
      <path id="Rectangle 399" d="M9.5 9C9.22386 9 9 8.77614 9 8.5L9 2L10 2L10 8.5C10 8.77614 9.77614 9 9.5 9Z" />
    </g>
  </svg>
</template>

<script setup lang="ts">

</script>

<style scoped>
svg {
  cursor: pointer;
}
</style>