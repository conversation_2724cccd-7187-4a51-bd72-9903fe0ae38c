import { BaseNode } from './BaseNode'

/**
 * API调用节点
 */
export class ApiNode extends BaseNode {
  constructor() {
    super({
      id: 'api',
      name: 'API调用',
      category: 'integration',
      icon: 'pi pi-send',
      color: '#E91E63',
      description: 'API接口调用节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      method: 'GET',
      url: '',
      headers: {
        'Content-Type': 'application/json'
      },
      body: {},
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000,
      authentication: {
        type: 'none', // none, basic, bearer, oauth2, apikey
        credentials: {}
      },
      responseMapping: {},
      errorHandling: {
        continueOnError: false,
        defaultValue: null
      }
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#C2185B',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.url || node.data.url.trim() === '') {
      errors.push('API节点必须指定URL')
    }

    try {
      new URL(node.data.url)
    } catch {
      errors.push('API URL格式不正确')
    }

    return errors
  }
}

/**
 * 通知节点
 */
export class NotificationNode extends BaseNode {
  constructor() {
    super({
      id: 'notification',
      name: '通知',
      category: 'integration',
      icon: 'pi pi-bell',
      color: '#FF5722',
      description: '消息通知节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      type: 'email', // email, sms, push, webhook, slack, dingtalk
      recipients: [],
      subject: '',
      content: '',
      template: '',
      variables: {},
      priority: 'normal', // low, normal, high, urgent
      retry: {
        enabled: true,
        maxAttempts: 3,
        delay: 5000
      }
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#D84315',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.recipients || node.data.recipients.length === 0) {
      errors.push('通知节点必须指定接收者')
    }

    if (!node.data.content && !node.data.template) {
      errors.push('通知节点必须指定内容或模板')
    }

    return errors
  }
}

/**
 * FTP节点
 */
export class FtpNode extends BaseNode {
  constructor() {
    super({
      id: 'ftp',
      name: 'FTP',
      category: 'integration',
      icon: 'pi pi-cloud-upload',
      color: '#795548',
      description: 'FTP文件传输节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      operation: 'upload', // upload, download, list, delete
      connection: {
        host: '',
        port: 21,
        username: '',
        password: '',
        secure: false, // FTPS
        passive: true
      },
      localPath: '',
      remotePath: '',
      overwrite: false,
      createDir: true
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#5D4037',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.connection.host) {
      errors.push('FTP节点必须指定主机地址')
    }

    if (!node.data.localPath && !node.data.remotePath) {
      errors.push('FTP节点必须指定本地路径或远程路径')
    }

    return errors
  }
}

/**
 * 队列节点
 */
export class QueueNode extends BaseNode {
  constructor() {
    super({
      id: 'queue',
      name: '队列',
      category: 'integration',
      icon: 'pi pi-list',
      color: '#9C27B0',
      description: '消息队列节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      operation: 'send', // send, receive, peek
      queueName: '',
      message: {},
      connection: {
        type: 'rabbitmq', // rabbitmq, activemq, kafka, redis
        host: 'localhost',
        port: 5672,
        username: 'guest',
        password: 'guest',
        vhost: '/'
      },
      options: {
        persistent: true,
        priority: 0,
        expiration: null,
        deliveryMode: 2
      }
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#7B1FA2',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.queueName || node.data.queueName.trim() === '') {
      errors.push('队列节点必须指定队列名称')
    }

    return errors
  }
}
