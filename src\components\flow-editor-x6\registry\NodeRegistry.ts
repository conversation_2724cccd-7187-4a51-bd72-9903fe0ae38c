import { Graph } from "@antv/x6";
import type { FlowNodeType } from "../types";

/**
 * 节点形状配置
 */
interface NodeShapeConfig {
  shape: string;
  inherit: string;
  defaultSize: { width: number; height: number };
  attrs: any;
  ports: any;
}

/**
 * 节点注册管理器
 */
export class NodeRegistry {
  private static instance: NodeRegistry;
  private nodeTypes = new Map<string, FlowNodeType>();
  private shapeConfigs = new Map<string, NodeShapeConfig>();
  private registered = false;

  private constructor() {}

  static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  /**
   * 注册节点类型
   */
  registerNodeType(nodeType: FlowNodeType): void {
    this.nodeTypes.set(nodeType.id, nodeType);
  }

  /**
   * 获取节点类型
   */
  getNodeType(id: string): FlowNodeType | undefined {
    return this.nodeTypes.get(id);
  }

  /**
   * 获取所有节点类型
   */
  getAllNodeTypes(): FlowNodeType[] {
    return Array.from(this.nodeTypes.values());
  }

  /**
   * 注册X6节点形状
   */
  registerX6Shapes(): void {
    if (this.registered) return;

    // 基础矩形节点
    Graph.registerNode(
      "flow-rect",
      {
        inherit: "rect",
        width: 120,
        height: 60,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
            rx: 8,
            ry: 8,
          },
          text: {
            fontSize: 12,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: this.getPortConfig(),
      },
      true
    );

    // 圆形节点
    Graph.registerNode(
      "flow-circle",
      {
        inherit: "circle",
        width: 80,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
          },
          text: {
            fontSize: 12,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: this.getPortConfig(),
      },
      true
    );

    // 菱形节点
    Graph.registerNode(
      "flow-polygon",
      {
        inherit: "polygon",
        width: 80,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
            refPoints: "0,10 10,0 20,10 10,20",
          },
          text: {
            fontSize: 12,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: this.getPortConfig(),
      },
      true
    );

    this.registered = true;
  }

  /**
   * 获取端口配置
   */
  private getPortConfig() {
    return {
      groups: {
        input: {
          position: "left",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#31d0c6",
              strokeWidth: 2,
              fill: "#fff",
              "port-group": "input",
              style: {
                visibility: "hidden",
              },
            },
          },
        },
        output: {
          position: "right",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#fe854f",
              strokeWidth: 2,
              fill: "#fff",
              "port-group": "output",
              style: {
                visibility: "hidden",
              },
            },
          },
        },
      },
      items: [
        {
          id: "input",
          group: "input",
        },
        {
          id: "output",
          group: "output",
        },
      ],
    };
  }

  /**
   * 根据节点类型获取X6形状
   */
  getX6Shape(nodeType: string): string {
    switch (nodeType) {
      case "start":
      case "end":
      case "timer":
        return "flow-circle";
      case "decision":
        return "flow-polygon";
      default:
        return "flow-rect";
    }
  }

  /**
   * 根据节点类型获取端口配置
   */
  getNodePorts(nodeType: string) {
    switch (nodeType) {
      case "start":
        return [
          {
            id: "output",
            group: "output",
          },
        ];
      case "end":
        return [
          {
            id: "input",
            group: "input",
          },
        ];
      case "decision":
        return [
          {
            id: "input",
            group: "input",
          },
          {
            id: "output-yes",
            group: "output",
          },
          {
            id: "output-no",
            group: "output",
          },
        ];
      default:
        return [
          {
            id: "input",
            group: "input",
          },
          {
            id: "output",
            group: "output",
          },
        ];
    }
  }

  /**
   * 清除注册状态（用于测试）
   */
  reset(): void {
    this.nodeTypes.clear();
    this.shapeConfigs.clear();
    this.registered = false;
  }
}

// 导出单例实例
export const nodeRegistry = NodeRegistry.getInstance();
