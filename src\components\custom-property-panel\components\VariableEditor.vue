<template>
  <a-modal v-model:visible="visible" okText="Run" cancelText="Cancel" body-class="variable-editor-modal"
    :mask-closable="false">
    <template #title>
      Flow Execution Parameters
    </template>
    <json-editor-vue v-model="json" :show-btns="true" :expandedOnStart="true"></json-editor-vue>
  </a-modal>
</template>

<script setup lang="ts">
import useModelerStore from "@/store/modeler"
import { ref } from "vue";
import JsonEditorVue from 'vue3-ts-jsoneditor'

const visible = ref(false);
const json = ref({
  test: 'test',
  test2: 'test',
  test3: 'test',
  test4: 'test',
})

const modelerStore = useModelerStore()

defineExpose({
  visible,
})
</script>

<style lang="scss">
.custom-property-panel {
  .arco-form {
    height: 200px
  }
}

.variable-editor-modal {
  .vue-ts-json-editor {
    height: 350px;

    .jse-last {
      display: none !important;
    }
  }


}
</style>