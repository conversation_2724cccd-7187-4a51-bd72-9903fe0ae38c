import require from "./base";

export function step(data: string) {
  return require(`/debug/step?data=${data}`);
}

export interface StartProcessParams {
  processDefinitionId?: string;
}

export function startProcess(params: StartProcessParams) {
  return require(`/debug/startProcess`, params);
}

export interface SetBreakPointsParams {
  breakpoints: {
    elementId?: string;
    processDefinitionId?: string;
    type?: string;
    condition?: {
      language?: string;
      script?: string;
    };
  }[];
}

export function setBreakPoints(params: SetBreakPointsParams) {
  return require(`/debug/setBreakPoints`, params);
}

export function getProcessDef(data: string) {
  return require(`/debug/getProcessDef?data=${data}`);
}

export interface EndDebugRes {
  code: string;
  message: string;
  data: Record<string, unknown>;
}
export function resume(data: any) {
  return require(`/debug/resume?data=${data}`);
}

export function endDebug(): Promise<EndDebugRes> {
  return require(`/debug/endDebug`);
}

export function debugDeploy(params: any) {
  return require(`/debug/deploy`, params);
}
