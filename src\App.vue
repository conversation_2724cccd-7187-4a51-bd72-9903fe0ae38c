<script setup lang="ts">
import bpmn from "./components/bpmn.vue";
import DebugBpmn from "./components/DebugBpmn.vue";
import Designer from "./components/Designer/Designer.vue";
import Test from "./components/TestKonva.vue";
import ColumnMap from "./components/column-map/index.vue";
import ChooseRange from "./components/choose-range/index.vue";
import ContextMenu from "primevue/contextmenu";
import RightMenu from "./components/column-map/menu.vue";
import gantt from "./components/gantt/index.vue";
import CromDemo from "./components/cron/demo.vue";
import DragTableDemo from "./components/drag-table/demo.vue";
import SimpleDragDemo from "./components/drag-table/simple-demo.vue";
import EnhancedDemo from "./components/drag-table/enhanced-demo.vue";
import RefactoredDemo from "./components/drag-table/refactored-demo.vue";
import TestHook from "./components/drag-table/test-hook.vue";
import MinimalTest from "./components/drag-table/minimal-test.vue";
import IdInputDemo from "./components/IdInput/demo.vue";
import FlowEditorDemo from "./components/flow-editor-x6/demo/FlowEditorDemo.vue";
import TestLayout from "./components/flow-editor-x6/demo/TestLayout.vue";
import TestPage from "./components/flow-editor-x6/test/TestPage.vue";

import { ref } from "vue";

const tables = [
  {
    tableName: "table1",
    position: {
      x: 100,
      y: 100,
    },
    fields: [
      {
        name: "name",
        type: "varchar",
      },
      {
        name: "age",
        type: "int",
      },
      {
        name: "gender",
        type: "varchar",
      },
      {
        name: "address",
        type: "varchar",
      },
    ],
  },
  {
    tableName: "table2",
    position: {
      x: 500,
      y: 100,
    },
    fields: [
      {
        name: "type",
        type: "varchar",
      },
      {
        name: "price",
        type: "int",
      },
      {
        name: "description",
        type: "varchar",
      },
    ],
  },
  {
    tableName: "table3",
    position: {
      x: 900,
      y: 100,
    },
    fields: [
      {
        name: "class",
        type: "varchar",
      },
      {
        name: "address",
        type: "int",
      },
      {
        name: "test",
        type: "varchar",
      },
    ],
  },
];
const initData: any = {
  lines: [
    {
      source: {
        field: "name",
        table: "table1",
      },
      target: {
        field: "price",
        table: "table2",
      },
    },
  ],
  directions: [{ from: "table1", to: "table2" }],
};
const meun = ref<InstanceType<typeof RightMenu>>();
const columnMap = ref<InstanceType<typeof ColumnMap>>();

function handleRightClick(event: MouseEvent, line: any) {
  meun.value!.showMenu(event, line);
}
</script>

<template>
  <div class="app-container">
    <TestPage />
  </div>
</template>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
}
</style>
