<template>
  <div class="drag-table-container">
    <DataTable
      ref="dataTableRef"
      :value="internalData"
      :columns="columns"
      :loading="loading"
      :paginator="paginator"
      :rows="rows"
      :totalRecords="totalRecords"
      :lazy="lazy"
      :rowClass="getRowClass"
      @page="onPage"
      @sort="onSort"
      @filter="onFilter"
      class="drag-table"
    >
      <template #header v-if="$slots.header">
        <slot name="header"></slot>
      </template>

      <Column
        v-for="col in columns"
        :key="col.field"
        :field="col.field"
        :header="col.header"
        :sortable="col.sortable"
        :style="col.style"
        :class="col.class"
      >
        <template #body="slotProps" v-if="col.bodyTemplate">
          <slot
            :name="col.bodyTemplate"
            :data="slotProps.data"
            :field="col.field"
            :index="slotProps.index"
          ></slot>
        </template>
        <template #body="slotProps" v-else>
          {{ getFieldValue(slotProps.data, col.field) }}
        </template>
      </Column>

      <template #footer v-if="$slots.footer">
        <slot name="footer"></slot>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import DataTable from "primevue/datatable";
import Column from "primevue/column";

// 定义接口
interface TableColumn {
  field: string;
  header: string;
  sortable?: boolean;
  style?: string | object;
  class?: string;
  bodyTemplate?: string;
}

interface DragTableProps {
  data: any[];
  columns: TableColumn[];
  loading?: boolean;
  paginator?: boolean;
  rows?: number;
  totalRecords?: number;
  lazy?: boolean;
  disabledField?: string; // 用于标识行是否disabled的字段名
  onDragEnd?: (newData: any[]) => void;
}

interface DragState {
  isDragging: boolean;
  dragIndex: number;
  dropIndex: number;
  dragElement: HTMLElement | null;
  placeholder: HTMLElement | null;
}

// Props
const props = withDefaults(defineProps<DragTableProps>(), {
  loading: false,
  paginator: false,
  rows: 10,
  totalRecords: 0,
  lazy: false,
  disabledField: "disabled",
});

// Emits
const emit = defineEmits<{
  page: [event: any];
  sort: [event: any];
  filter: [event: any];
  dragEnd: [newData: any[]];
}>();

// Refs
const dataTableRef = ref();
const internalData = ref([...props.data]);

// 拖拽状态
const dragState = ref<DragState>({
  isDragging: false,
  dragIndex: -1,
  dropIndex: -1,
  dragElement: null,
  placeholder: null,
});

// 计算属性
const disabledRows = computed(() => {
  return internalData.value
    .map((item, index) => ({
      index,
      disabled: item[props.disabledField] || false,
    }))
    .filter((item) => item.disabled);
});

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    internalData.value = [...newData];
  },
  { deep: true }
);

// 获取字段值
const getFieldValue = (data: any, field: string) => {
  return field.split(".").reduce((obj, key) => obj?.[key], data);
};

// 获取行样式类
const getRowClass = (data: any) => {
  const classes = [];
  if (data[props.disabledField]) {
    classes.push("disabled-row");
  }
  return classes.join(" ");
};

// 事件处理
const onPage = (event: any) => {
  emit("page", event);
};

const onSort = (event: any) => {
  emit("sort", event);
};

const onFilter = (event: any) => {
  emit("filter", event);
};

// 存储事件监听器引用，用于清理
const eventListeners = ref<
  Map<HTMLElement, { [key: string]: (e: Event) => void }>
>(new Map());

// 清理事件监听器
const cleanupEventListeners = () => {
  eventListeners.value.forEach((handlers, element) => {
    Object.entries(handlers).forEach(([event, handler]) => {
      element.removeEventListener(event, handler);
    });
  });
  eventListeners.value.clear();
};

// 拖拽功能实现
const initDragAndDrop = () => {
  nextTick(() => {
    const tableBody =
      dataTableRef.value?.$el?.querySelector(".p-datatable-tbody");
    if (!tableBody) return;

    // 清理之前的事件监听器
    cleanupEventListeners();

    const rows = tableBody.querySelectorAll("tr");
    rows.forEach((row: HTMLElement, index: number) => {
      // 如果是disabled行，不允许拖拽
      if (internalData.value[index]?.[props.disabledField]) {
        row.draggable = false;
        return;
      }

      row.draggable = true;
      row.dataset.index = index.toString();

      // 创建事件处理器
      const handlers = {
        dragstart: (e: Event) => handleDragStart(e as DragEvent, index),
        dragover: (e: Event) => handleDragOver(e as DragEvent),
        drop: (e: Event) => handleDrop(e as DragEvent, index),
        dragend: (e: Event) => handleDragEnd(),
        dragenter: (e: Event) => handleDragEnter(e as DragEvent, index),
        dragleave: (e: Event) => handleDragLeave(e as DragEvent),
      };

      // 添加事件监听器
      Object.entries(handlers).forEach(([event, handler]) => {
        row.addEventListener(event, handler);
      });

      // 存储引用用于清理
      eventListeners.value.set(row, handlers);
    });
  });
};

const handleDragStart = (e: DragEvent, index: number) => {
  // 检查是否是disabled行
  if (internalData.value[index]?.[props.disabledField]) {
    e.preventDefault();
    return;
  }

  dragState.value.isDragging = true;
  dragState.value.dragIndex = index;
  dragState.value.dragElement = e.target as HTMLElement;

  // 添加拖拽样式
  (e.target as HTMLElement).classList.add("dragging");

  // 设置拖拽数据
  e.dataTransfer!.effectAllowed = "move";
  e.dataTransfer!.setData("text/html", (e.target as HTMLElement).outerHTML);
};

const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  e.dataTransfer!.dropEffect = "move";
};

const handleDragEnter = (e: DragEvent, index: number) => {
  e.preventDefault();

  // 如果是disabled行，不允许在其上方或下方放置
  if (internalData.value[index]?.[props.disabledField]) {
    return;
  }

  // 添加拖拽悬停样式
  (e.target as HTMLElement).closest("tr")?.classList.add("drag-over");
  dragState.value.dropIndex = index;
};

const handleDragLeave = (e: DragEvent) => {
  // 移除拖拽悬停样式
  (e.target as HTMLElement).closest("tr")?.classList.remove("drag-over");
};

const handleDrop = (e: DragEvent, dropIndex: number) => {
  e.preventDefault();

  const dragIndex = dragState.value.dragIndex;

  // 如果拖拽到disabled行，不允许放置
  if (internalData.value[dropIndex]?.[props.disabledField]) {
    return;
  }

  if (dragIndex !== -1 && dragIndex !== dropIndex) {
    // 执行拖拽重排逻辑
    const newData = reorderWithDisabledRows(dragIndex, dropIndex);
    internalData.value = newData;
    emit("dragEnd", newData);
    props.onDragEnd?.(newData);
  }

  // 清理样式
  cleanupDragStyles();
};

const handleDragEnd = () => {
  cleanupDragStyles();
  resetDragState();
};

const cleanupDragStyles = () => {
  const tableBody =
    dataTableRef.value?.$el?.querySelector(".p-datatable-tbody");
  if (tableBody) {
    const rows = tableBody.querySelectorAll("tr");
    rows.forEach((row: HTMLElement) => {
      row.classList.remove("dragging", "drag-over");
    });
  }
};

const resetDragState = () => {
  dragState.value = {
    isDragging: false,
    dragIndex: -1,
    dropIndex: -1,
    dragElement: null,
    placeholder: null,
  };
};

// 核心重排逻辑：处理disabled行的特殊情况
const reorderWithDisabledRows = (fromIndex: number, toIndex: number): any[] => {
  const newData = [...internalData.value];
  const draggedItem = newData[fromIndex];

  // 获取所有disabled行的位置信息
  const disabledPositions = new Set<number>();
  newData.forEach((item, index) => {
    if (item[props.disabledField]) {
      disabledPositions.add(index);
    }
  });

  // 如果目标位置是disabled行，不允许操作
  if (disabledPositions.has(toIndex)) {
    return newData;
  }

  // 移除被拖拽的元素
  newData.splice(fromIndex, 1);

  // 计算实际插入位置（考虑disabled行）
  let actualInsertIndex = toIndex;

  // 如果拖拽方向是向下（fromIndex < toIndex）
  if (fromIndex < toIndex) {
    actualInsertIndex = toIndex;
  } else {
    // 如果拖拽方向是向上（fromIndex > toIndex）
    actualInsertIndex = toIndex;
  }

  // 插入到新位置
  newData.splice(actualInsertIndex, 0, draggedItem);

  // 确保disabled行回到原来的位置
  const originalDisabledItems: { item: any; originalIndex: number }[] = [];

  // 收集原始disabled项目及其位置
  internalData.value.forEach((item, index) => {
    if (item[props.disabledField]) {
      originalDisabledItems.push({ item, originalIndex: index });
    }
  });

  // 从新数组中移除所有disabled项目
  const enabledItems = newData.filter((item) => !item[props.disabledField]);

  // 重新构建数组，将disabled项目放回原位置
  const finalData: any[] = [];
  let enabledIndex = 0;

  for (let i = 0; i < internalData.value.length; i++) {
    const originalDisabled = originalDisabledItems.find(
      (d) => d.originalIndex === i
    );
    if (originalDisabled) {
      // 在原位置插入disabled项目
      finalData.push(originalDisabled.item);
    } else {
      // 插入enabled项目
      if (enabledIndex < enabledItems.length) {
        finalData.push(enabledItems[enabledIndex]);
        enabledIndex++;
      }
    }
  }

  return finalData;
};

// 生命周期
onMounted(() => {
  initDragAndDrop();
});

onUnmounted(() => {
  // 清理事件监听器
  cleanupEventListeners();
});

// 监听数据变化，重新初始化拖拽
watch(
  internalData,
  () => {
    nextTick(() => {
      initDragAndDrop();
    });
  },
  { deep: true }
);
</script>

<style scoped>
.drag-table-container {
  position: relative;
}

.drag-table :deep(.p-datatable-tbody tr) {
  cursor: grab;
  transition: all 0.2s ease;
}

.drag-table :deep(.p-datatable-tbody tr:hover) {
  background-color: var(--surface-hover);
}

.drag-table :deep(.p-datatable-tbody tr.disabled-row) {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--surface-100);
}

.drag-table :deep(.p-datatable-tbody tr.disabled-row:hover) {
  background-color: var(--surface-100);
}

.drag-table :deep(.p-datatable-tbody tr.dragging) {
  opacity: 0.5;
  transform: rotate(2deg);
  z-index: 1000;
  cursor: grabbing;
}

.drag-table :deep(.p-datatable-tbody tr.drag-over) {
  border-top: 2px solid var(--primary-color);
}

.drag-placeholder {
  height: 2px;
  background-color: var(--primary-color);
  margin: 2px 0;
  border-radius: 1px;
}
</style>
