<template>
  <div
    class="test-page"
    style="width: 100vw; height: 100vh; display: flex; flex-direction: column"
  >
    <div class="bg-white border-b border-gray-200 p-4">
      <h1 class="text-xl font-bold">流程编辑器功能测试</h1>
      <div class="mt-2 text-sm text-gray-600">
        <p>测试项目：</p>
        <ul class="list-disc list-inside mt-1">
          <li>✅ 工具栏显示</li>
          <li>🔍 端口悬停显示 (鼠标悬停节点查看端口)</li>
          <li>
            ⌨️ 快捷键功能 (Ctrl+Z撤销, Ctrl+Y重做, Ctrl+C复制, Ctrl+V粘贴,
            Delete删除)
          </li>
          <li>🏷️ 连线标签编辑 (双击连线)</li>
          <li>🔗 拖拽连线 (从输出端口拖到输入端口)</li>
        </ul>
        <p class="mt-2 text-blue-600">
          💡 提示：先添加几个测试节点，然后尝试上述功能
        </p>
      </div>
    </div>

    <div style="flex: 1">
      <FlowEditor
        v-model="flowData"
        :node-types="nodeTypes"
        @node-click="handleNodeClick"
        @edge-click="handleEdgeClick"
        @validation-change="handleValidationChange"
      />
    </div>

    <div class="bg-gray-100 border-t border-gray-200 p-2 text-sm">
      <div class="flex justify-between">
        <div>
          <span>节点数: {{ nodeCount }}</span>
          <span class="ml-4">连线数: {{ edgeCount }}</span>
        </div>
        <div>
          <button
            @click="addTestNode"
            class="px-3 py-1 bg-blue-500 text-white rounded mr-2"
          >
            添加测试节点
          </button>
          <button
            @click="clearAll"
            class="px-3 py-1 bg-red-500 text-white rounded"
          >
            清空
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import FlowEditor from "../index.vue";
import type {
  FlowNodeType,
  FlowNode,
  FlowEdge,
  ValidationResult,
} from "../types";

// 数据
const flowData = ref<any>(null);

// 简单的节点类型
const nodeTypes = ref<FlowNodeType[]>([
  {
    id: "start",
    name: "开始",
    category: "control",
    icon: "pi pi-play",
    color: "#4CAF50",
    description: "流程开始节点",
    defaultSize: { width: 100, height: 60 },
    defaultProps: {},
    validationRules: [],
    allowedConnections: {
      input: [],
      output: ["*"],
    },
  },
  {
    id: "process",
    name: "处理",
    category: "process",
    icon: "pi pi-cog",
    color: "#2196F3",
    description: "处理节点",
    defaultSize: { width: 120, height: 60 },
    defaultProps: {},
    validationRules: [],
    allowedConnections: {
      input: ["*"],
      output: ["*"],
    },
  },
  {
    id: "end",
    name: "结束",
    category: "control",
    icon: "pi pi-stop",
    color: "#F44336",
    description: "流程结束节点",
    defaultSize: { width: 100, height: 60 },
    defaultProps: {},
    validationRules: [],
    allowedConnections: {
      input: ["*"],
      output: [],
    },
  },
]);

// 计算属性
const nodeCount = computed(() => {
  return flowData.value?.nodes?.length || 0;
});

const edgeCount = computed(() => {
  return flowData.value?.edges?.length || 0;
});

// 事件处理
const handleNodeClick = (node: FlowNode) => {
  console.log("节点点击:", node);
};

const handleEdgeClick = (edge: FlowEdge) => {
  console.log("连线点击:", edge);
};

const handleValidationChange = (results: ValidationResult[]) => {
  console.log("验证结果:", results);
};

// 测试方法
let nodeCounter = 0;
const addTestNode = () => {
  nodeCounter++;
  const newNode = {
    id: `test-node-${nodeCounter}`,
    type: "process",
    label: `测试节点 ${nodeCounter}`,
    position: { x: 100 + nodeCounter * 150, y: 100 },
    size: { width: 120, height: 60 },
    data: {},
    style: {},
    ports: [],
    validationState: "valid",
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  };

  if (!flowData.value) {
    flowData.value = { nodes: [], edges: [] };
  }

  flowData.value.nodes.push(newNode);
};

const clearAll = () => {
  flowData.value = { nodes: [], edges: [] };
  nodeCounter = 0;
};

// 初始化一些测试数据
const initTestData = () => {
  flowData.value = {
    nodes: [
      {
        id: "start-1",
        type: "start",
        label: "开始",
        position: { x: 100, y: 100 },
        size: { width: 100, height: 60 },
        data: {},
        style: {},
        ports: [],
        validationState: "valid",
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      },
      {
        id: "process-1",
        type: "process",
        label: "处理",
        position: { x: 300, y: 100 },
        size: { width: 120, height: 60 },
        data: {},
        style: {},
        ports: [],
        validationState: "valid",
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      },
    ],
    edges: [],
  };
};

// 初始化
initTestData();
</script>

<style scoped>
.test-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
</style>
