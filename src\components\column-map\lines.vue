<template>
  <svg style="
        position: absolute;
        left: 0;
        top: 0;
        /* pointer-events: none; */
        overflow: visible;
        width: 1px;
        height: 1px;
        z-index: 100;
      " ref="svgRef">
    <line v-for="(line, index) in lines" :key="'line-' + line.position.y1" :x1="line.position.x1" :y1="line.position.y1"
      :x2="line.position.x2" :y2="line.position.y2" stroke="black" stroke-width="3" :style="getLineDirection(line.data)"
      @contextmenu.prevent="handleRightClick($event, line)" />
    <!-- 虚线 -->
    <line :x1="currentLine.x1" :y1="currentLine.y1" :x2="currentLine.x2" :y2="currentLine.y2" stroke="black"
      stroke-dasharray="5,5" stroke-width="2" />

    <defs>
      <!-- 结束箭头 方向与线方向相同 -->
      <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="strokeWidth" markerWidth="8"
        markerHeight="6" orient="auto-start-reverse">
        <path d="M 0 0 L 10 5 L 0 10 z" fill="black" />
      </marker>
    </defs>
  </svg>
</template>

<script setup lang="ts">
import { inject, onMounted, toRaw } from 'vue';
import { TableDirctionMap, SourceTargetMap, showLineData } from "./types";

const lines = inject('lines') as showLineData
const tableDirctionMap = inject('tableDirctionMap') as TableDirctionMap;
const currentLine = inject('currentLine') as any;
const emits = defineEmits(["handle-right-click"])

function getLineDirection(line: SourceTargetMap) {
  let result = "";
  // 1.判断线的table是否在tableDirctionMap中
  const sourceTable = line.source.table;
  const targetTable = line.target.table;
  // 如果有指定方向 判断方向并返回指定的箭头
  tableDirctionMap.forEach(item => {
    if ([item.from, item.to].includes(sourceTable) && [item.from, item.to].includes(targetTable)) {
      if (item.from === sourceTable && item.to === targetTable) {
        result += 'marker-end: url(#arrow);'
      } else if (item.from === targetTable && item.to === sourceTable) {
        result += 'marker-start: url(#arrow);'
      }
    }
  })
  return result
}

function handleRightClick(event: MouseEvent, line: any) {
  emits("handle-right-click", event, toRaw(line))
}
</script>

<style scoped></style>