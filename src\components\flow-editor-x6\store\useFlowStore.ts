import { ref, reactive, computed, watch } from 'vue'
import type { 
  FlowNode, 
  FlowEdge, 
  FlowData, 
  FlowConfig, 
  FlowStoreState,
  ValidationResult,
  HistoryRecord,
  FlowPosition
} from '../types'
import { generateId } from '../utils/common'
import { createDefaultConfig } from '../utils/config'

export function useFlowStore() {
  // 状态定义
  const state = reactive<FlowStoreState>({
    nodes: new Map(),
    edges: new Map(),
    selectedNodes: new Set(),
    selectedEdges: new Set(),
    clipboard: {
      nodes: [],
      edges: []
    },
    history: [],
    historyIndex: -1,
    config: createDefaultConfig(),
    validationResults: [],
    isDirty: false
  })

  // 计算属性
  const allNodes = computed(() => Array.from(state.nodes.values()))
  const allEdges = computed(() => Array.from(state.edges.values()))
  const selectedNodeList = computed(() => 
    Array.from(state.selectedNodes).map(id => state.nodes.get(id)).filter(Boolean) as FlowNode[]
  )
  const selectedEdgeList = computed(() => 
    Array.from(state.selectedEdges).map(id => state.edges.get(id)).filter(Boolean) as FlowEdge[]
  )
  const canUndo = computed(() => state.historyIndex >= 0)
  const canRedo = computed(() => state.historyIndex < state.history.length - 1)

  // 节点操作
  const addNode = (node: FlowNode) => {
    if (state.nodes.has(node.id)) {
      throw new Error(`Node with id ${node.id} already exists`)
    }
    
    state.nodes.set(node.id, { ...node })
    recordHistory('add', 'node', node.id, undefined, node)
    markDirty()
  }

  const removeNode = (nodeId: string) => {
    const node = state.nodes.get(nodeId)
    if (!node) return

    // 移除相关的边
    const relatedEdges = allEdges.value.filter(edge => 
      edge.source === nodeId || edge.target === nodeId
    )
    relatedEdges.forEach(edge => removeEdge(edge.id))

    state.nodes.delete(nodeId)
    state.selectedNodes.delete(nodeId)
    recordHistory('delete', 'node', nodeId, node, undefined)
    markDirty()
  }

  const updateNode = (nodeId: string, updates: Partial<FlowNode>) => {
    const node = state.nodes.get(nodeId)
    if (!node) return

    const oldNode = { ...node }
    const newNode = { ...node, ...updates }
    state.nodes.set(nodeId, newNode)
    recordHistory('update', 'node', nodeId, oldNode, newNode)
    markDirty()
  }

  const getNode = (nodeId: string): FlowNode | undefined => {
    return state.nodes.get(nodeId)
  }

  // 边操作
  const addEdge = (edge: FlowEdge) => {
    if (state.edges.has(edge.id)) {
      throw new Error(`Edge with id ${edge.id} already exists`)
    }

    // 验证源节点和目标节点是否存在
    if (!state.nodes.has(edge.source) || !state.nodes.has(edge.target)) {
      throw new Error('Source or target node does not exist')
    }

    state.edges.set(edge.id, { ...edge })
    recordHistory('add', 'edge', edge.id, undefined, edge)
    markDirty()
  }

  const removeEdge = (edgeId: string) => {
    const edge = state.edges.get(edgeId)
    if (!edge) return

    state.edges.delete(edgeId)
    state.selectedEdges.delete(edgeId)
    recordHistory('delete', 'edge', edgeId, edge, undefined)
    markDirty()
  }

  const updateEdge = (edgeId: string, updates: Partial<FlowEdge>) => {
    const edge = state.edges.get(edgeId)
    if (!edge) return

    const oldEdge = { ...edge }
    const newEdge = { ...edge, ...updates }
    state.edges.set(edgeId, newEdge)
    recordHistory('update', 'edge', edgeId, oldEdge, newEdge)
    markDirty()
  }

  const getEdge = (edgeId: string): FlowEdge | undefined => {
    return state.edges.get(edgeId)
  }

  // 选择操作
  const selectNode = (nodeId: string, multiple = false) => {
    if (!multiple) {
      state.selectedNodes.clear()
      state.selectedEdges.clear()
    }
    state.selectedNodes.add(nodeId)
  }

  const selectEdge = (edgeId: string, multiple = false) => {
    if (!multiple) {
      state.selectedNodes.clear()
      state.selectedEdges.clear()
    }
    state.selectedEdges.add(edgeId)
  }

  const deselectNode = (nodeId: string) => {
    state.selectedNodes.delete(nodeId)
  }

  const deselectEdge = (edgeId: string) => {
    state.selectedEdges.delete(edgeId)
  }

  const clearSelection = () => {
    state.selectedNodes.clear()
    state.selectedEdges.clear()
  }

  // 历史记录
  const recordHistory = (
    action: HistoryRecord['action'],
    type: 'node' | 'edge',
    id: string,
    before?: any,
    after?: any
  ) => {
    const record: HistoryRecord = {
      id: generateId(),
      timestamp: Date.now(),
      action,
      target: { type, id },
      before,
      after
    }

    // 清除当前位置之后的历史记录
    state.history = state.history.slice(0, state.historyIndex + 1)
    state.history.push(record)
    state.historyIndex = state.history.length - 1

    // 限制历史记录数量
    const maxHistory = 100
    if (state.history.length > maxHistory) {
      state.history = state.history.slice(-maxHistory)
      state.historyIndex = state.history.length - 1
    }
  }

  const undo = () => {
    if (!canUndo.value) return

    const record = state.history[state.historyIndex]
    applyHistoryRecord(record, true)
    state.historyIndex--
  }

  const redo = () => {
    if (!canRedo.value) return

    state.historyIndex++
    const record = state.history[state.historyIndex]
    applyHistoryRecord(record, false)
  }

  const applyHistoryRecord = (record: HistoryRecord, reverse: boolean) => {
    const { action, target, before, after } = record
    const data = reverse ? before : after
    const reverseAction = reverse

    switch (action) {
      case 'add':
        if (reverseAction) {
          if (target.type === 'node') {
            state.nodes.delete(target.id)
          } else {
            state.edges.delete(target.id)
          }
        } else {
          if (target.type === 'node') {
            state.nodes.set(target.id, data)
          } else {
            state.edges.set(target.id, data)
          }
        }
        break
      case 'delete':
        if (reverseAction) {
          if (target.type === 'node') {
            state.nodes.set(target.id, data)
          } else {
            state.edges.set(target.id, data)
          }
        } else {
          if (target.type === 'node') {
            state.nodes.delete(target.id)
          } else {
            state.edges.delete(target.id)
          }
        }
        break
      case 'update':
        if (target.type === 'node') {
          state.nodes.set(target.id, data)
        } else {
          state.edges.set(target.id, data)
        }
        break
    }
  }

  // 数据导入导出
  const exportData = (): FlowData => {
    return {
      nodes: allNodes.value,
      edges: allEdges.value,
      metadata: {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      config: state.config
    }
  }

  const importData = (data: FlowData) => {
    state.nodes.clear()
    state.edges.clear()
    clearSelection()

    data.nodes.forEach(node => state.nodes.set(node.id, node))
    data.edges.forEach(edge => state.edges.set(edge.id, edge))
    
    if (data.config) {
      state.config = { ...state.config, ...data.config }
    }

    state.history = []
    state.historyIndex = -1
    markDirty()
  }

  const clearAll = () => {
    state.nodes.clear()
    state.edges.clear()
    clearSelection()
    state.history = []
    state.historyIndex = -1
    state.validationResults = []
    markDirty()
  }

  // 工具方法
  const markDirty = () => {
    state.isDirty = true
  }

  const markClean = () => {
    state.isDirty = false
  }

  const updateValidationResults = (results: ValidationResult[]) => {
    state.validationResults = results
  }

  return {
    // 状态
    state,
    
    // 计算属性
    allNodes,
    allEdges,
    selectedNodeList,
    selectedEdgeList,
    canUndo,
    canRedo,

    // 节点操作
    addNode,
    removeNode,
    updateNode,
    getNode,

    // 边操作
    addEdge,
    removeEdge,
    updateEdge,
    getEdge,

    // 选择操作
    selectNode,
    selectEdge,
    deselectNode,
    deselectEdge,
    clearSelection,

    // 历史操作
    undo,
    redo,

    // 数据操作
    exportData,
    importData,
    clearAll,

    // 工具方法
    markDirty,
    markClean,
    updateValidationResults
  }
}
