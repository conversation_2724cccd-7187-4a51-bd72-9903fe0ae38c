import dayjs from "dayjs";

export const mockData: any[] = [];

// 使用函数生成1w条数据
function generateMockData(num: number) {
  for (let i = 0; i < num; i++) {
    const id = i + 1;
    const name = `任务${id}`;
    const list = [];
    // 第一个时间在2022-01-01 00:00:00 - 2022-01-01 03:00:00 之间
    const time1 = dayjs("2022-01-01 19:00:00").add(
      Math.floor(Math.random() * 1 * 60),
      "minute"
    );
    // 第二个时间在2022-01-01 03:00:00 - 2022-01-01 5:00:00 之间
    const time2 = dayjs("2022-01-01 22:00:00").add(
      Math.floor(Math.random() * 2 * 60),
      "minute"
    );
    // 生成3个time1-time2之间的时间
    const time1_2_1 = dayjs(time1).add(
      Math.floor(Math.random() * 1 * 60),
      "minute"
    );
    const lot1 = {
      label: "子任务1",
      startTime: time1.format("YYYY-MM-DD HH:mm:ss"),
      endTime: time1_2_1.format("YYYY-MM-DD HH:mm:ss"),
      legendKey: "style1",
    };
    const time1_2_2 = dayjs(time1_2_1).add(
      Math.floor(Math.random() * 1 * 60),
      "minute"
    );
    const lot2 = {
      label: "子任务1",
      startTime: time1_2_1.format("YYYY-MM-DD HH:mm:ss"),
      endTime: time1_2_2.format("YYYY-MM-DD HH:mm:ss"),
      legendKey: "style1",
    };
    const lot3 = {
      label: "子任务1",
      startTime: time1_2_2.format("YYYY-MM-DD HH:mm:ss"),
      endTime: time2.format("YYYY-MM-DD HH:mm:ss"),
      legendKey: "style1",
    };
    list.push({
      label: "子任务1",
      startTime: time1.format("YYYY-MM-DD HH:mm:ss"),
      endTime: time2.format("YYYY-MM-DD HH:mm:ss"),
      legendKey: "style1",
      detailList: [lot1, lot2, lot3],
    });

    // 第三个时间在2022-01-01 5:00:00 - 2022-01-01 9:00:00 之间
    const time3 = dayjs("2022-01-02 00:00:00").add(
      Math.floor(Math.random() * 1 * 60),
      "minute"
    );
    // 第四个时间在2022-01-01 9:00:00 - 2022-01-01 12:00:00 之间
    const time4 = dayjs("2022-01-02 1:00:00").add(
      Math.floor(Math.random() * 3 * 60),
      "minute"
    );
    list.push({
      label: "子任务2",
      startTime: time3.format("YYYY-MM-DD HH:mm:ss"),
      endTime: time4.format("YYYY-MM-DD HH:mm:ss"),
      legendKey: "style2",
    });
    // 第五个时间在2022-01-01 12:00:00 - 2022-01-01 16:00:00 之间
    const time5 = dayjs("2022-01-02 4:00:00").add(
      Math.floor(Math.random() * 1 * 60),
      "minute"
    );
    // 第六个时间在2022-01-01 16:00:00 - 2022-01-01 20:00:00 之间
    const time6 = dayjs("2022-01-02 5:00:00").add(
      Math.floor(Math.random() * 2 * 60),
      "minute"
    );
    list.push({
      label: "子任务3",
      startTime: time5.format("YYYY-MM-DD HH:mm:ss"),
      endTime: time6.format("YYYY-MM-DD HH:mm:ss"),
      legendKey: "style3",
    });
    mockData.push({ id, name, list });
  }
  console.log(
    "%c [ mockData ]-61",
    "font-size:13px; background:pink; color:#bf2c9f;",
    mockData
  );
}

generateMockData(999);
