import { LineData, SourceTargetMap } from "./types";

export function getRawId(options: { tableName: string; rowId: string }) {
  return `table-${options.tableName}-${options.rowId}`;
}

export function getAnchorId(options: {
  rowId: string;
  direction: string;
  tableName: string;
}) {
  return `${getRawId({ tableName: options.tableName, rowId: options.rowId })}-${
    options.direction
  }-anchor`;
}

// 获取锚点的位置 如果是左锚点 则获取左边边界的位置 如果是右锚点 则获取右边边界的位置
export function getAnchorPosition(
  tableName: string,
  rowId: string,
  direction: string
) {
  const anchorId = getAnchorId({ tableName, rowId, direction });
  const anchor = document.getElementById(anchorId);
  if (!anchor) return { x: 0, y: 0 };
  const rect = anchor.getBoundingClientRect();
  const y = rect.top + rect.height / 2;
  const x = direction === "left" ? rect.left : rect.right;
  return { x, y };
}

export function getLinePosition(source: LineData, target: LineData) {
  const sourceLeftAnchor = getAnchorPosition(
    source.table,
    source.field,
    "left"
  );
  const sourceRightAnchor = getAnchorPosition(
    source.table,
    source.field,
    "right"
  );
  const targetLeftAnchor = getAnchorPosition(
    target.table,
    target.field,
    "left"
  );
  const targetRightAnchor = getAnchorPosition(
    target.table,
    target.field,
    "right"
  );
  if (
    !(
      sourceLeftAnchor &&
      sourceRightAnchor &&
      targetLeftAnchor &&
      targetRightAnchor
    )
  )
    return { x1: 0, y1: 0, x2: 0, y2: 0 };
  // 对比各个锚点的位置
  // 1. source右锚点在target左锚点的左边
  if (sourceRightAnchor.x < targetLeftAnchor.x)
    return {
      x1: sourceRightAnchor.x,
      y1: sourceRightAnchor.y,
      x2: targetLeftAnchor.x,
      y2: targetLeftAnchor.y,
    };
  // 2. source左锚点在target右锚点的右边
  if (sourceLeftAnchor.x > targetRightAnchor.x)
    return {
      x1: sourceLeftAnchor.x,
      y1: sourceLeftAnchor.y,
      x2: targetRightAnchor.x,
      y2: targetRightAnchor.y,
    };
  // 其余情况 返回 source右锚点和target左锚点的位置
  return {
    x1: sourceRightAnchor.x,
    y1: sourceRightAnchor.y,
    x2: targetRightAnchor.x,
    y2: targetRightAnchor.y,
  };
}

export function isLineDataValid(data1: LineData, data2: LineData) {
  if (!data1.field || !data1.table || !data2.field || !data2.table)
    return false;
  if (data1.table === data2.table) return false;
  return true;
}

export function isLineDataSame(data1: LineData, data2: LineData) {
  if (data1.table !== data2.table) return false;
  if (data1.field !== data2.field) return false;
  return true;
}
