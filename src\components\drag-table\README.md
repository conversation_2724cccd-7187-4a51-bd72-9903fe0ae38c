# 增强版拖拽表格组件

## 概述

这是一个功能完善、结构清晰的拖拽表格组件，采用数据驱动的设计模式，具有优秀的用户体验和强大的功能特性。

## 核心特性

### 🎯 智能拖拽逻辑
- **精确位置控制**：支持在行的上半部分或下半部分放置，提供精确的插入位置控制
- **禁用行智能处理**：当拖拽到禁用行时，自动找到最近的有效插入位置
- **视觉反馈**：实时显示插入位置（蓝色边框指示上方/下方）
- **拖拽状态指示**：右上角显示当前拖拽的项目信息

### 🔒 禁用行保护
- 禁用行位置永远不变，确保数据完整性
- 禁用行不能被拖拽，也不能作为拖拽目标
- 启用行可以在禁用行周围自由重排序

### 🎨 优秀的用户体验
- **丰富的视觉反馈**：拖拽源、悬停目标、插入位置都有明确的视觉指示
- **响应式设计**：适配移动端和桌面端
- **无障碍支持**：键盘导航和屏幕阅读器支持
- **调试模式**：开发时可开启调试信息显示

### 🛠 开发友好
- **TypeScript支持**：完整的类型定义
- **插槽系统**：灵活的自定义列模板
- **事件系统**：完整的拖拽事件回调
- **数据驱动**：响应式数据绑定

## 使用方法

### 基础用法

```vue
<template>
  <EnhancedDragTable
    v-model:data="tableData"
    :columns="columns"
    key-field="id"
    display-field="name"
    disabled-field="disabled"
    @row-reorder="handleReorder"
  />
</template>

<script setup>
import EnhancedDragTable from './enhanced-drag-table.vue'

const tableData = ref([
  { id: 1, name: '项目1', disabled: false },
  { id: 2, name: '项目2', disabled: true },
  { id: 3, name: '项目3', disabled: false }
])

const columns = [
  { field: 'name', header: '名称' },
  { field: 'status', header: '状态', template: 'status' }
]

const handleReorder = (event) => {
  console.log('重排序完成:', event.value)
}
</script>
```

### 自定义列模板

```vue
<template>
  <EnhancedDragTable
    v-model:data="tableData"
    :columns="columns"
  >
    <!-- 状态列自定义模板 -->
    <template #status="{ data }">
      <span :class="data.disabled ? 'text-danger' : 'text-success'">
        {{ data.disabled ? '禁用' : '启用' }}
      </span>
    </template>
    
    <!-- 操作列自定义模板 -->
    <template #actions="{ data, index }">
      <button @click="editItem(index)">编辑</button>
      <button @click="deleteItem(index)">删除</button>
    </template>
  </EnhancedDragTable>
</template>
```

## Props 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `data` | `Array` | `[]` | 表格数据数组 |
| `columns` | `Array` | `[]` | 列配置数组 |
| `keyField` | `String` | `'id'` | 唯一标识字段 |
| `displayField` | `String` | `'name'` | 显示字段（用于拖拽提示） |
| `disabledField` | `String` | `'disabled'` | 禁用状态字段 |
| `debug` | `Boolean` | `false` | 是否开启调试模式 |

## 列配置

```javascript
const columns = [
  {
    field: 'name',        // 字段名
    header: '项目名称',    // 表头显示文本
    template: 'custom',   // 自定义模板名称（可选）
    style: { width: '200px' }, // 列样式（可选）
    class: 'text-center'  // 列CSS类（可选）
  }
]
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:data` | `Array` | 数据更新时触发 |
| `row-reorder` | `{ originalEvent, value }` | 行重排序完成时触发 |

## 拖拽行为说明

### 正常拖拽
- 将启用行拖拽到其他启用行的上方或下方
- 根据鼠标位置自动判断插入位置

### 拖拽到禁用行
- **拖拽到禁用行上方**：插入到该禁用行前面的最后一个启用行之后
- **拖拽到禁用行下方**：插入到该禁用行后面的第一个启用行之前

### 示例场景
原始顺序：`1, 2, 3(禁用), 4(禁用), 5, 6`

- 将"1"拖拽到"3"上方 → 结果：`2, 1, 3(禁用), 4(禁用), 5, 6`
- 将"1"拖拽到"4"下方 → 结果：`2, 3(禁用), 4(禁用), 1, 5, 6`
- 将"5"拖拽到"6"上方 → 结果：`1, 2, 3(禁用), 4(禁用), 6, 5`

## 样式自定义

组件使用CSS变量，可以轻松自定义主题：

```css
.enhanced-drag-table {
  --primary-color: #007bff;
  --hover-color: #f8f9fa;
  --disabled-color: #f5f5f5;
  --border-color: #dee2e6;
}
```

## 最佳实践

1. **数据结构**：确保每个数据项都有唯一的`id`字段
2. **性能优化**：对于大量数据，考虑使用虚拟滚动
3. **用户体验**：为禁用行提供清晰的视觉指示
4. **错误处理**：监听拖拽事件，处理可能的错误情况

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发调试

开启调试模式可以查看详细的拖拽信息：

```vue
<EnhancedDragTable :debug="true" />
```

调试信息包括：
- 拖拽源索引和项目
- 当前悬停位置
- 计算出的插入位置
