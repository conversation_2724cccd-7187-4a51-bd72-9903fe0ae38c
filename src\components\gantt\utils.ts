export type ShowItem = {
  label: string;
  startTime: string;
  endTime: string;
};

export type ShowGanttItem = ShowItem & {
  detailList?: ShowGanttItem[];
  legendKey: string;
};

export type GanttData = {
  id: number;
  name: string;
  list: ShowGanttItem[];
};

export const stepMap = [
  {
    timeRange: 12 * 60 * 60 * 1000,
    step: 1 * 60 * 60 * 1000,
  },
  {
    timeRange: 6 * 60 * 60 * 1000,
    step: 30 * 60 * 1000,
  },
  {
    timeRange: 3 * 60 * 60 * 1000,
    step: 15 * 60 * 1000,
  },
  {
    timeRange: 1 * 60 * 60 * 1000,
    step: 5 * 60 * 1000,
  },
];
export function getTaskId(rowId: string, taskData: ShowItem) {
  // 正则表达式替换 将字符串中的. _ : 转为 -
  const label = taskData.label.replace(/[._:]/g, "-");
  return `gantt-task-${rowId}-${taskData.label}`;
}
