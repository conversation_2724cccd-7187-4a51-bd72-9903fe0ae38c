# Flow Editor X6

基于 AntV X6 的流程可视化编辑器，支持数据驱动开发、多种节点类型、自定义样式、拖拽操作和完整的验证系统。

## 特性

- 🎨 **可视化编辑**: 基于 X6 的强大图形编辑能力
- 🧩 **模块化设计**: 清晰的代码结构，易于扩展和维护
- 🎯 **数据驱动**: 支持数组驱动的节点和连线管理
- 🔧 **可扩展**: 支持自定义节点类型、验证规则和样式
- 🖱️ **拖拽支持**: 从表格拖拽行生成节点
- ✅ **验证系统**: 完整的流程验证和错误提示
- 🎨 **样式自定义**: 支持节点和连线的样式定制
- 📱 **响应式**: 适配不同屏幕尺寸

## 目录结构

```
flow-editor-x6/
├── index.vue                    # 主组件
├── types/                       # 类型定义
│   └── index.ts
├── store/                       # 状态管理
│   └── useFlowStore.ts
├── utils/                       # 工具函数
│   ├── common.ts
│   ├── config.ts
│   ├── nodeFactory.ts
│   └── edgeFactory.ts
├── nodes/                       # 节点类型系统
│   └── nodeTypes.ts
├── composables/                 # 组合式函数
│   ├── useFlowGraph.ts
│   ├── useFlowValidation.ts
│   └── useFlowDragDrop.ts
├── components/                  # UI组件
│   ├── FlowControlPanel.vue
│   ├── FlowToolbar.vue
│   ├── FlowPropertyPanel.vue
│   └── FlowValidationOverlay.vue
├── validation/                  # 验证系统
│   └── ValidationRuleManager.ts
└── demo/                       # 演示页面
    └── FlowEditorDemo.vue
```

## 快速开始

### 基本使用

```vue
<template>
  <div class="h-screen">
    <FlowEditor
      v-model="flowData"
      :node-types="nodeTypes"
      @node-click="handleNodeClick"
      @edge-click="handleEdgeClick"
      @validation-change="handleValidationChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FlowEditor from './components/flow-editor-x6/index.vue'
import type { FlowNodeType, FlowNode, FlowEdge, ValidationResult } from './components/flow-editor-x6/types'

const flowData = ref(null)
const nodeTypes = ref<FlowNodeType[]>([])

const handleNodeClick = (node: FlowNode) => {
  console.log('Node clicked:', node)
}

const handleEdgeClick = (edge: FlowEdge) => {
  console.log('Edge clicked:', edge)
}

const handleValidationChange = (results: ValidationResult[]) => {
  console.log('Validation results:', results)
}
</script>
```

### 自定义节点类型

```typescript
import { nodeTypeRegistry } from './components/flow-editor-x6/nodes/nodeTypes'

// 注册自定义节点类型
nodeTypeRegistry.register({
  id: 'custom-approval',
  name: '审批节点',
  category: 'process',
  icon: 'pi pi-check-square',
  color: '#8B5CF6',
  description: '自定义审批流程节点',
  defaultSize: { width: 140, height: 80 },
  defaultProps: {
    style: {
      borderRadius: 8,
      fill: '#8B5CF6',
      stroke: '#7C3AED',
      strokeWidth: 2
    },
    approvers: [],
    approvalType: 'sequential'
  },
  allowedConnections: {
    input: ['*'],
    output: ['*']
  },
  validationRules: []
})
```

### 自定义验证规则

```typescript
import { validationRuleManager } from './components/flow-editor-x6/validation/ValidationRuleManager'

// 添加自定义验证规则
validationRuleManager.register({
  id: 'custom-rule',
  name: '自定义规则',
  type: 'node',
  validator: async (context) => {
    const { node } = context
    if (!node) return null
    
    // 自定义验证逻辑
    if (node.type === 'approval' && !node.data.approvers?.length) {
      return {
        id: `node-${node.id}-no-approvers`,
        ruleId: 'custom-rule',
        type: 'error',
        message: '审批节点必须指定审批人',
        target: { type: 'node', id: node.id },
        suggestions: ['添加审批人']
      }
    }
    
    return null
  },
  enabled: true,
  priority: 10,
  description: '检查审批节点是否配置了审批人'
})
```

### 表格拖拽集成

```vue
<template>
  <div class="flex">
    <!-- 表格数据 -->
    <div class="w-80 p-4">
      <div
        v-for="item in tableData"
        :key="item.id"
        :draggable="true"
        @dragstart="handleDragStart(item, $event)"
        class="p-3 bg-gray-100 rounded mb-2 cursor-move"
      >
        {{ item.name }}
      </div>
    </div>
    
    <!-- 流程编辑器 -->
    <div class="flex-1">
      <FlowEditor v-model="flowData" />
    </div>
  </div>
</template>

<script setup lang="ts">
const tableData = ref([
  { id: 1, name: '用户注册', type: 'process' },
  { id: 2, name: '邮件验证', type: 'notification' },
  // ...
])

const handleDragStart = (item: any, event: DragEvent) => {
  const dragData = {
    type: 'node',
    nodeType: item.type,
    data: item
  }
  
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(dragData))
  }
}
</script>
```

## API 参考

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| modelValue | any | - | 流程数据 |
| readonly | boolean | false | 是否只读 |
| nodeTypes | FlowNodeType[] | [] | 自定义节点类型 |

### Events

| 事件 | 参数 | 描述 |
|------|------|------|
| update:modelValue | value: any | 流程数据更新 |
| node-click | node: FlowNode | 节点点击 |
| edge-click | edge: FlowEdge | 连线点击 |
| validation-change | results: ValidationResult[] | 验证结果变化 |

### 类型定义

#### FlowNode

```typescript
interface FlowNode {
  id: string
  type: string
  label: string
  position: FlowPosition
  size: FlowSize
  data: Record<string, any>
  style?: Record<string, any>
  ports?: FlowPort[]
  validationState?: ValidationState
  metadata?: Record<string, any>
}
```

#### FlowEdge

```typescript
interface FlowEdge {
  id: string
  source: string
  target: string
  sourcePort?: string
  targetPort?: string
  label?: string
  data: Record<string, any>
  style?: Record<string, any>
  validationState?: ValidationState
  metadata?: Record<string, any>
}
```

#### FlowNodeType

```typescript
interface FlowNodeType {
  id: string
  name: string
  category: string
  icon?: string
  color?: string
  description?: string
  defaultSize?: FlowSize
  defaultProps?: Record<string, any>
  validationRules?: ValidationRule[]
  customComponent?: string
  allowedConnections?: {
    input?: string[]
    output?: string[]
  }
}
```

## 内置节点类型

- **start**: 开始节点
- **end**: 结束节点
- **process**: 处理节点
- **decision**: 判断节点
- **parallel**: 并行节点
- **merge**: 合并节点
- **timer**: 定时器节点
- **webhook**: Webhook节点
- **database**: 数据库节点
- **api**: API调用节点
- **script**: 脚本节点
- **notification**: 通知节点

## 内置验证规则

- **no-orphan-nodes**: 检查孤立节点
- **no-cycles**: 检查循环依赖
- **start-node-required**: 必须有开始节点
- **end-node-required**: 必须有结束节点
- **node-label-required**: 节点标签必填
- **decision-node-branches**: 判断节点分支检查
- **node-size-validation**: 节点大小验证
- **edge-connection-validation**: 连线有效性验证

## 开发指南

### 添加新的节点类型

1. 在 `nodes/nodeTypes.ts` 中定义节点类型
2. 在 `composables/useFlowGraph.ts` 中注册 X6 节点形状
3. 添加相应的验证规则（可选）

### 添加新的验证规则

1. 在 `validation/ValidationRuleManager.ts` 中实现验证函数
2. 注册到验证规则管理器
3. 在组件中启用规则

### 自定义样式

通过修改节点类型的 `defaultProps.style` 或在运行时更新节点的 `style` 属性来自定义样式。

## 许可证

MIT License
