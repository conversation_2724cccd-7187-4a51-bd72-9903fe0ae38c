import "./debug.scss";
import { isShapeNode, canBePaused } from "@/utils";
import useOverlayManager from "./overlay-manager";
import useModelerStore from "@/store/modeler";

export function usedebugEvent(eventBus: any, modeler: any) {
  const debugModeler = modeler;
  const modelerStore = useModelerStore();
  const { addOverlay, removeOverlay } = useOverlayManager(modeler);
  eventBus.on("element.click", (event: any) => {
    const element = event.element;
    if (modelerStore.isRunning) {
      modelerStore.setActiveElement(element.id);
    }
  });
  // 设置悬浮事件
  eventBus.on("element.hover", (event: any) => {
    if (!canBePaused(event.element) || modelerStore.isRunning) return;
    // console.log('%c [ element.hover ]-76', 'font-size:13px; background:pink; color:#bf2c9f;')
    const element = event.element;
    // 鼠标悬浮时添加断点图标
    addOverlay(element, "break-point");
  });
  // 设置移出事件
  eventBus.on("element.out", (event: any) => {
    const element = event.element;
    if (
      !canBePaused(event.element) ||
      !modelerStore.overlayerManager[element.id]
    )
      return;
    // console.log('%c [ element.out ]-76', 'font-size:13px; background:pink; color:#bf2c9f;')
    modelerStore.overlayerManager[element.id].isHover--;
    // 延迟移除断点图标
    setTimeout(() => {
      removeOverlay(element);
    }, 200);
  });
  // eventBus.on("selection.changed", function (e: any) {
  //   console.log(
  //     "%c [ e ]-40",
  //     "font-size:13px; background:pink; color:#bf2c9f;",
  //     e
  //   );
  //   var selectedElements = e.newSelection;

  //   // 如果有选中的元素，你可以处理它们
  //   if (selectedElements.length) {
  //     var selectedElement = selectedElements[0]; // 获取第一个选中的元素
  //     // 进行你想要执行的操作，例如打印ID
  //     console.log("Selected element:", selectedElement.id);
  //   } else {
  //     // 没有元素被选中
  //     console.log("No elements are selected.");
  //   }
  // });
}
