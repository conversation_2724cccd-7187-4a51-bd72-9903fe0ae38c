<template>
    <div class="containers">
        <div id="canvas" ref="canvas"></div>
        <button @click="checkoutExec" style="position: absolute; top: 20px; left: 300px;">执行</button>
        <div id="js-properties-panel" class="panel"></div>
    </div>
</template>

<script setup lang="ts">
// 在vue组件中引入bpmn
import { ref, onMounted } from 'vue';
import { xmlStr3 } from './data/xmlStr';
import BpmnModeler from 'bpmn-js/lib/Modeler';
// 引入模块
import {
    BpmnPropertiesPanelModule,
    BpmnPropertiesProviderModule,
} from 'bpmn-js-properties-panel';
// import CamundaBpmnModdle from 'camunda-bpmn-moddle/resources/camunda.json'
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda.json';
// 引入执行流程动画
import tokenSimulationPlugin from '../bpmn-js-token-simulation-lib';

import './style/index.scss'

const modeler: any = ref<BpmnModeler>();
const canvas: any = ref(null);

// 只读模式配置
const readonlyMode = {
    paletteProvider: ["value", ''],//禁用/清空左侧工具栏
    labelEditingProvider: ["value", ''],//禁用节点编辑
    contextPadProvider: ["value", ''],//禁用图形菜单
    bendpoints: ["value", {}],//禁用连线拖动
    // zoomScroll: ["value", ''],//禁用滚动
    // moveCanvas:['value',''],//禁用拖动整个流程图
    move: ['value', '']//禁用单个图形拖动
}

async function init() {
    modeler.value = new BpmnModeler({
        container: canvas.value,
        propertiesPanel: {
            parent: '#js-properties-panel'
        },
        // additionalModules的作用是添加额外的模块 常见的模块有： 撤销重做、拖拽、放大缩小、导入导出等
        additionalModules: [
            BpmnPropertiesPanelModule,
            BpmnPropertiesProviderModule,
            tokenSimulationPlugin
        ],
        //moddleExtensions的作用是添加额外的模型 例如camunda的模型 用于扩展bpmn的模型
        moddleExtensions: {
            camunda: camundaModdleDescriptor
        },
    });
    await createNewDiagram()
    const eventBus: any = modeler.value.get('eventBus');
    // 点击节点 输出该节点对应的xml中的标签信息
    eventBus.on('element.click', function (element: any) {
        console.log('%c [ element ]-62', 'font-size:13px; background:pink; color:#bf2c9f;', element)
    });
    // 获取tokenSimulation的eventBus
    // const simulator = modeler.value.get('contextPads')
    // console.log('%c [ simulator ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', simulator)
    // 监听tokenSimulation.pauseSimulation的事件
    // simulator._eventBus.on('tokenSimulation.pauseSimulation', function (e: any) {
    //     console.log('tokenSimulation.pauseSimulation', e)
    // })
    // simulator._eventBus.on('tokenSimulation.simulator.createScope', function (e: any) {
    //     console.log('tokenSimulation.simulator.createScope', e)
    // })
    // toggleMode._eventBus.on('tokenSimulation.pauseSimulation', function (e: any) {
    //     console.log('tokenSimulation.pauseSimulation', e)
    // })
}

async function createNewDiagram() {
    try {
        const res = await modeler.value?.importXML(xmlStr3);
        console.log('%c [ res ]-47', 'font-size:13px; background:pink; color:#bf2c9f;', res)
    } catch (error) {
        console.error(error)
    }
}


function checkoutExec() {
    // const animation = modeler.value.get('animation')
    // const animationBus = animation._eventBus
    // const pauseSimulation = modeler.value.get('pauseSimulation')
    // animationBus.on('tokenSimulation.playSimulation', function (e: any) {
    //     console.log('TokenSimulation.playSimulation', e)
    //     pauseSimulation.pause()
    // })
    // animationBus.on('tokenSimulation.pauseSimulation', function (e: any) {
    //     console.log('TokenSimulation.pauseSimulation', e)
    //     // pauseSimulation.pause()
    // })
}
onMounted(() => {
    init();
});
</script>

<style lang="scss" scoped>
.containers {
    position: absolute;
    /* background-color: #1f1a1a; */
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

#canvas {
    width: 100%;
    height: 100%;
}

#js-properties-panel {
    width: 300px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-color: #f0f0f0;
    border-left: 1px solid #ccc;
    overflow: auto;
    z-index: 100;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    box-sizing: border-box;
}
</style>./data/xmlStr