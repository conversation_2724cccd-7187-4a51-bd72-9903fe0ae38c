// DebugOverlay.ts
import { injectable } from 'inversify'; // 如果你使用依赖注入
import { domify, attr as domAttr, classes as domClasses, event as domEvent, remove as domRemove, query as domQuery } from 'min-dom';
import {merge, isString} from 'lodash';

const OVERLAY_HTML = `
  <div class="dbg-controls">
    <span class="break"></span>
    <span class="resume"></span>
  </div>
`;

const MARKER_STEP_ACTIVE = 'dbg-step-active',
      MARKER_BREAKPOINT_ACTIVE = 'dbg-breakpoint-active';

const DEFAULT_OPTIONS = {
  buttons: {
    'break': {
      title: 'toggle breakpoint',
      className: 'icon-break',
      text: ''
    },
    'resume': {
      title: 'continue execution',
      className: 'icon-resume',
      text: ''
    }
  },
  overlayHtml: OVERLAY_HTML
};

// 使用@Inject装饰器添加依赖项，如果你使用的是类似inversify这样的IOC容器
// @injectable() 
class DebugOverlay {

  private _eventBus: any;
  private _elementRegistry: any;
  private _overlays: any;
  private _canvas: any;

  private _breakpoints: Record<string, boolean> = {};
  private _overlay: HTMLElement;

  constructor(config: any, eventBus: any, overlays: any, elementRegistry: any, canvas: any) {
    const mergedConfig = merge({}, DEFAULT_OPTIONS, config);
    this._eventBus = eventBus;
    this._elementRegistry = elementRegistry;
    this._overlays = overlays;
    this._canvas = canvas;

    this._overlay = domify(mergedConfig.overlayHtml) as HTMLElement;

    this.initializeOverlayEvents();
  }

  private initializeOverlayEvents(): void {
    // 下面是事件监听的实现...
  }

  public toggleBreakpoint(element: any): void {
    element = isString(element) ? this._elementRegistry.get(element) : element;
    this._eventBus.fire('debug.breakpoint.toggle', { element: element });
  }

  public resume(element: any): void {
    element = isString(element) ? this._elementRegistry.get(element) : element;
    this._eventBus.fire('debug.resume', { element: element });
  }
}

export default DebugOverlay;
