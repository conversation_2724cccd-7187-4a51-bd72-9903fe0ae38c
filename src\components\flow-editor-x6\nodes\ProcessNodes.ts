import { BaseNode } from './BaseNode'

/**
 * 处理节点
 */
export class ProcessNode extends BaseNode {
  constructor() {
    super({
      id: 'process',
      name: '处理',
      category: 'process',
      icon: 'pi pi-cog',
      color: '#2196F3',
      description: '业务处理节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000,
      async: false
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#1976D2',
      strokeWidth: 2
    }
  }
}

/**
 * 脚本节点
 */
export class ScriptNode extends BaseNode {
  constructor() {
    super({
      id: 'script',
      name: '脚本',
      category: 'process',
      icon: 'pi pi-code',
      color: '#673AB7',
      description: '自定义脚本节点',
      defaultSize: { width: 120, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      language: 'javascript',
      code: '',
      timeout: 30000,
      sandbox: true
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#512DA8',
      strokeWidth: 2
    }
  }
}

/**
 * 审批节点
 */
export class ApprovalNode extends BaseNode {
  constructor() {
    super({
      id: 'approval',
      name: '审批',
      category: 'process',
      icon: 'pi pi-check-square',
      color: '#8B5CF6',
      description: '审批流程节点',
      defaultSize: { width: 140, height: 80 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      approvers: [],
      approvalType: 'sequential', // sequential, parallel, any
      timeout: 86400000, // 24小时
      autoApprove: false,
      escalation: {
        enabled: false,
        timeout: 172800000, // 48小时
        escalateTo: []
      }
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 8,
      fill: this.color,
      stroke: '#7C3AED',
      strokeWidth: 2
    }
  }

  validate(node: any): string[] {
    const errors = super.validate(node)
    
    if (!node.data.approvers || node.data.approvers.length === 0) {
      errors.push('审批节点必须指定至少一个审批人')
    }

    return errors
  }
}
