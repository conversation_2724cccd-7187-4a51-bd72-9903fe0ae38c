<template>
  <div>
    <div class="containers">
      <Palette />
      <div v-show="modelerStore.getIsDebugMode" ref="debugBpmnContainer" class="bpmn-canvas"></div>
      <div v-show="!modelerStore.getIsDebugMode" ref="editBpmnContainer" class="bpmn-canvas"></div>
      <button @click="toggleMode('debug')" style="position: absolute; top: 20px; left: 300px;"
        :class="modelerStore.isDebugMode ? 'acitve' : ''">debug模式</button>
      <button @click="toggleMode('edit')" style="position: absolute; top: 20px; left: 500px;"
        :class="!modelerStore.isDebugMode ? 'acitve' : ''">编辑模式</button>
      <CustomPropertyPanel />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import Palette from '@/components/palette/index.vue';
import CustomPropertyPanel from '@/components/custom-property-panel/Panel.vue';
import initModeler from './initModeler';
import useModelerStore from '@/store/modeler'
import { debugApi, changeSessionId } from '@/api'

const {
  debugBpmnContainer,
  editBpmnContainer,
  init
} = initModeler()

// 初始化两个实例
onMounted(async () => {
  init()
});

const modelerStore = useModelerStore()

watch(() => modelerStore.activeElementId, (newVal, oldVal) => {
  if (oldVal) {
    modelerStore.clearHightlight(oldVal as any)
  }
  if (newVal) {
    modelerStore.setHightlight(newVal as any)
  }
})

async function toggleMode(mode: string) {
  const newMode = mode === 'debug'
  // 判断模式是否变化，如果没有变化，则直接返回
  if (newMode === modelerStore.getIsDebugMode) {
    return
  }
  if (newMode) {
    // 进入debug模式
    const res = await debugApi.debugDeploy({
      resourceName: 'process.bpmn',
      resourceData: modelerStore.getXmlStr
    })
    modelerStore.processDefId = res.processDefId
    changeSessionId(res.sessionId)
  }
  await modelerStore.changeMode(newMode);
}

</script>

<style lang="scss" scoped>
.containers {
  position: absolute;
  /* background-color: #1f1a1a; */
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;

  .acitve {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  }
}

.bpmn-canvas {
  // width: 100%;
  flex: 1;
  height: 100%;
}
</style>