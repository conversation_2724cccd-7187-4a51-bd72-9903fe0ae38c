import type { FlowNodeType, FlowNode, FlowPosition, FlowSize, ValidationRule } from '../types'
import { generateId } from '../utils/common'

/**
 * 基础节点类
 */
export abstract class BaseNode {
  public readonly id: string
  public readonly name: string
  public readonly category: string
  public readonly icon?: string
  public readonly color: string
  public readonly description?: string
  public readonly defaultSize: FlowSize
  public readonly allowedConnections: {
    input: string[]
    output: string[]
  }

  constructor(config: {
    id: string
    name: string
    category: string
    icon?: string
    color: string
    description?: string
    defaultSize?: FlowSize
    allowedConnections?: {
      input?: string[]
      output?: string[]
    }
  }) {
    this.id = config.id
    this.name = config.name
    this.category = config.category
    this.icon = config.icon
    this.color = config.color
    this.description = config.description
    this.defaultSize = config.defaultSize || { width: 120, height: 60 }
    this.allowedConnections = {
      input: config.allowedConnections?.input || ['*'],
      output: config.allowedConnections?.output || ['*']
    }
  }

  /**
   * 获取默认属性
   */
  abstract getDefaultProps(): Record<string, any>

  /**
   * 获取默认样式
   */
  abstract getDefaultStyle(): Record<string, any>

  /**
   * 获取验证规则
   */
  getValidationRules(): ValidationRule[] {
    return []
  }

  /**
   * 创建节点实例
   */
  createInstance(position: FlowPosition, customData?: Record<string, any>): FlowNode {
    return {
      id: generateId('node'),
      type: this.id,
      label: this.name,
      position: { ...position },
      size: { ...this.defaultSize },
      data: { ...this.getDefaultProps(), ...customData },
      style: { ...this.getDefaultStyle() },
      ports: this.createPorts(),
      validationState: 'valid',
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }
  }

  /**
   * 创建端口
   */
  protected createPorts() {
    return [
      {
        id: 'top',
        group: 'input' as const,
        position: { x: 0, y: 0 },
        label: '输入'
      },
      {
        id: 'right',
        group: 'output' as const,
        position: { x: 0, y: 0 },
        label: '输出'
      },
      {
        id: 'bottom',
        group: 'output' as const,
        position: { x: 0, y: 0 },
        label: '输出'
      },
      {
        id: 'left',
        group: 'input' as const,
        position: { x: 0, y: 0 },
        label: '输入'
      }
    ]
  }

  /**
   * 转换为FlowNodeType
   */
  toFlowNodeType(): FlowNodeType {
    return {
      id: this.id,
      name: this.name,
      category: this.category,
      icon: this.icon,
      color: this.color,
      description: this.description,
      defaultSize: this.defaultSize,
      defaultProps: this.getDefaultProps(),
      validationRules: this.getValidationRules(),
      allowedConnections: this.allowedConnections
    }
  }

  /**
   * 验证节点数据
   */
  validate(node: FlowNode): string[] {
    const errors: string[] = []

    if (!node.label || node.label.trim() === '') {
      errors.push('节点标签不能为空')
    }

    if (node.size.width < 50) {
      errors.push('节点宽度不能小于50px')
    }

    if (node.size.height < 30) {
      errors.push('节点高度不能小于30px')
    }

    return errors
  }

  /**
   * 获取X6节点形状
   */
  getX6Shape(): string {
    return 'flow-rect'
  }

  /**
   * 是否可以连接到指定节点类型
   */
  canConnectTo(targetNodeType: string): boolean {
    return this.allowedConnections.output.includes('*') || 
           this.allowedConnections.output.includes(targetNodeType)
  }

  /**
   * 是否可以接受来自指定节点类型的连接
   */
  canAcceptFrom(sourceNodeType: string): boolean {
    return this.allowedConnections.input.includes('*') || 
           this.allowedConnections.input.includes(sourceNodeType)
  }
}
