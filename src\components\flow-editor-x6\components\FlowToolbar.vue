<template>
  <div
    class="flow-toolbar flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200"
  >
    <!-- 左侧工具组 -->
    <div class="flex items-center space-x-2">
      <!-- 文件操作 -->
      <div class="flex items-center space-x-1 border-r border-gray-200 pr-2">
        <button
          @click="$emit('save')"
          class="toolbar-btn"
          title="保存 (Ctrl+S)"
        >
          <i class="pi pi-save" />
        </button>

        <button @click="handleLoad" class="toolbar-btn" title="加载">
          <i class="pi pi-folder-open" />
        </button>

        <button @click="$emit('clear')" class="toolbar-btn" title="清空">
          <i class="pi pi-trash" />
        </button>
      </div>

      <!-- 编辑操作 -->
      <div class="flex items-center space-x-1 border-r border-gray-200 pr-2">
        <button
          @click="undo"
          :disabled="!canUndo"
          class="toolbar-btn"
          title="撤销 (Ctrl+Z)"
        >
          <i class="pi pi-undo" />
        </button>

        <button
          @click="redo"
          :disabled="!canRedo"
          class="toolbar-btn"
          title="重做 (Ctrl+Y)"
        >
          <i class="pi pi-redo" />
        </button>

        <button
          @click="copy"
          :disabled="!hasSelection"
          class="toolbar-btn"
          title="复制 (Ctrl+C)"
        >
          <i class="pi pi-copy" />
        </button>

        <button
          @click="paste"
          :disabled="!canPaste"
          class="toolbar-btn"
          title="粘贴 (Ctrl+V)"
        >
          <i class="pi pi-clone" />
        </button>

        <button
          @click="deleteSelected"
          :disabled="!hasSelection"
          class="toolbar-btn"
          title="删除 (Delete)"
        >
          <i class="pi pi-times" />
        </button>
      </div>

      <!-- 布局操作 -->
      <div class="flex items-center space-x-1 border-r border-gray-200 pr-2">
        <button @click="autoLayout" class="toolbar-btn" title="自动布局">
          <i class="pi pi-sitemap" />
        </button>

        <button
          @click="alignLeft"
          :disabled="!hasMultipleSelection"
          class="toolbar-btn"
          title="左对齐"
        >
          <i class="pi pi-align-left" />
        </button>

        <button
          @click="alignCenter"
          :disabled="!hasMultipleSelection"
          class="toolbar-btn"
          title="居中对齐"
        >
          <i class="pi pi-align-center" />
        </button>

        <button
          @click="alignRight"
          :disabled="!hasMultipleSelection"
          class="toolbar-btn"
          title="右对齐"
        >
          <i class="pi pi-align-right" />
        </button>
      </div>

      <!-- 视图操作 -->
      <div class="flex items-center space-x-1">
        <button @click="zoomIn" class="toolbar-btn" title="放大">
          <i class="pi pi-search-plus" />
        </button>

        <span class="text-sm text-gray-600 min-w-[60px] text-center">
          {{ Math.round(zoomLevel * 100) }}%
        </span>

        <button @click="zoomOut" class="toolbar-btn" title="缩小">
          <i class="pi pi-search-minus" />
        </button>

        <button @click="zoomToFit" class="toolbar-btn" title="适应画布">
          <i class="pi pi-expand" />
        </button>

        <button @click="resetZoom" class="toolbar-btn" title="重置缩放">
          <i class="pi pi-refresh" />
        </button>
      </div>
    </div>

    <!-- 右侧工具组 -->
    <div class="flex items-center space-x-2">
      <!-- 网格和对齐 -->
      <div class="flex items-center space-x-1 border-l border-gray-200 pl-2">
        <button
          @click="toggleGrid"
          :class="{ 'bg-blue-100 text-blue-600': showGrid }"
          class="toolbar-btn"
          title="显示网格"
        >
          <i class="pi pi-th" />
        </button>

        <button
          @click="toggleSnap"
          :class="{ 'bg-blue-100 text-blue-600': snapToGrid }"
          class="toolbar-btn"
          title="对齐网格"
        >
          <i class="pi pi-stop" />
        </button>
      </div>

      <!-- 验证 -->
      <div class="flex items-center space-x-1 border-l border-gray-200 pl-2">
        <button
          @click="$emit('validate')"
          class="toolbar-btn relative"
          title="验证流程"
        >
          <i class="pi pi-check-circle" />
          <span
            v-if="validationCount > 0"
            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center"
          >
            {{ validationCount > 9 ? "9+" : validationCount }}
          </span>
        </button>
      </div>

      <!-- 设置 -->
      <div class="flex items-center space-x-1 border-l border-gray-200 pl-2">
        <button
          @click="showSettings = !showSettings"
          class="toolbar-btn"
          title="设置"
        >
          <i class="pi pi-cog" />
        </button>
      </div>
    </div>

    <!-- 设置面板 -->
    <div
      v-if="showSettings"
      class="absolute top-full right-4 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
    >
      <div class="p-4">
        <h4 class="font-medium text-gray-800 mb-3">画布设置</h4>

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <label class="text-sm text-gray-600">显示网格</label>
            <input v-model="showGrid" type="checkbox" class="rounded" />
          </div>

          <div class="flex items-center justify-between">
            <label class="text-sm text-gray-600">对齐网格</label>
            <input v-model="snapToGrid" type="checkbox" class="rounded" />
          </div>

          <div class="flex items-center justify-between">
            <label class="text-sm text-gray-600">实时验证</label>
            <input
              v-model="realtimeValidation"
              type="checkbox"
              class="rounded"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, onMounted, onUnmounted } from "vue";

// Emits
const emit = defineEmits<{
  save: [];
  load: [data: any];
  clear: [];
  validate: [];
}>();

// 注入store
const flowStore = inject("flowStore") as any;

// 响应式数据
const showSettings = ref(false);
const zoomLevel = ref(1);
const showGrid = ref(true);
const snapToGrid = ref(true);
const realtimeValidation = ref(true);
const validationCount = ref(0);

// 计算属性
const canUndo = computed(() => flowStore?.canUndo.value || false);
const canRedo = computed(() => flowStore?.canRedo.value || false);
const hasSelection = computed(() => {
  return (
    (flowStore?.selectedNodeList.value?.length || 0) > 0 ||
    (flowStore?.selectedEdgeList.value?.length || 0) > 0
  );
});
const hasMultipleSelection = computed(() => {
  return (flowStore?.selectedNodeList.value?.length || 0) > 1;
});
const canPaste = computed(() => {
  return (
    (flowStore?.state.clipboard.nodes?.length || 0) > 0 ||
    (flowStore?.state.clipboard.edges?.length || 0) > 0
  );
});

// 方法
const undo = () => {
  flowStore?.undo();
};

const redo = () => {
  flowStore?.redo();
};

const copy = () => {
  if (!hasSelection.value) return;

  const selectedNodes = flowStore?.selectedNodeList.value || [];
  const selectedEdges = flowStore?.selectedEdgeList.value || [];

  if (flowStore?.state.clipboard) {
    flowStore.state.clipboard.nodes = [...selectedNodes];
    flowStore.state.clipboard.edges = [...selectedEdges];
  }
};

const paste = () => {
  if (!canPaste.value) return;

  const clipboard = flowStore?.state.clipboard;
  if (clipboard && (clipboard.nodes.length > 0 || clipboard.edges.length > 0)) {
    // 粘贴节点，偏移位置避免重叠
    clipboard.nodes.forEach((node: any) => {
      const newNode = {
        ...node,
        id: `${node.id}_copy_${Date.now()}`,
        position: {
          x: node.position.x + 20,
          y: node.position.y + 20,
        },
      };
      flowStore?.addNode(newNode);
    });
  }
};

const deleteSelected = () => {
  if (!hasSelection.value) return;

  const selectedNodes = flowStore?.selectedNodeList.value || [];
  const selectedEdges = flowStore?.selectedEdgeList.value || [];

  selectedEdges.forEach((edge: any) => {
    flowStore?.removeEdge(edge.id);
  });

  selectedNodes.forEach((node: any) => {
    flowStore?.removeNode(node.id);
  });
};

const autoLayout = () => {
  // 实现自动布局
  console.log("Auto layout");
};

const alignLeft = () => {
  // 实现左对齐
  console.log("Align left");
};

const alignCenter = () => {
  // 实现居中对齐
  console.log("Align center");
};

const alignRight = () => {
  // 实现右对齐
  console.log("Align right");
};

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 3);
};

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.1);
};

const zoomToFit = () => {
  // 实现适应画布
  console.log("Zoom to fit");
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

const toggleGrid = () => {
  showGrid.value = !showGrid.value;
};

const toggleSnap = () => {
  snapToGrid.value = !snapToGrid.value;
};

const handleLoad = () => {
  // 创建文件输入
  const input = document.createElement("input");
  input.type = "file";
  input.accept = ".json";
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          emit("load", data);
        } catch (error) {
          console.error("Failed to load file:", error);
        }
      };
      reader.readAsText(file);
    }
  };
  input.click();
};

// 键盘快捷键
const handleKeydown = (e: KeyboardEvent) => {
  if (e.ctrlKey || e.metaKey) {
    switch (e.key) {
      case "s":
        e.preventDefault();
        emit("save");
        break;
      case "z":
        e.preventDefault();
        if (e.shiftKey) {
          redo();
        } else {
          undo();
        }
        break;
      case "y":
        e.preventDefault();
        redo();
        break;
      case "c":
        e.preventDefault();
        copy();
        break;
      case "v":
        e.preventDefault();
        paste();
        break;
    }
  } else if (e.key === "Delete") {
    deleteSelected();
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener("keydown", handleKeydown);

  // 监听点击外部关闭设置面板
  document.addEventListener("click", (e) => {
    if (showSettings.value && !(e.target as Element).closest(".flow-toolbar")) {
      showSettings.value = false;
    }
  });
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
.toolbar-btn {
  @apply flex items-center justify-center w-8 h-8 rounded hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800;
}

.toolbar-btn:disabled {
  @apply text-gray-400 cursor-not-allowed hover:bg-transparent hover:text-gray-400;
}

.toolbar-btn.active {
  @apply bg-blue-100 text-blue-600;
}
</style>
