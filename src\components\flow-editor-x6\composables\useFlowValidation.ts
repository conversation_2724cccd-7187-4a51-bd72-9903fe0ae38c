import { ref, computed } from 'vue'
import type { 
  ValidationRule, 
  ValidationResult, 
  ValidationContext,
  FlowNode,
  FlowEdge
} from '../types'
import { detectCycle } from '../utils/edgeFactory'

export function useFlowValidation(flowStore: any) {
  const validationResults = ref<ValidationResult[]>([])
  const isValidating = ref(false)

  // 内置验证规则
  const builtInRules: ValidationRule[] = [
    {
      id: 'no-orphan-nodes',
      name: '无孤立节点',
      type: 'flow',
      validator: validateNoOrphanNodes,
      enabled: true,
      priority: 1,
      description: '检查是否存在没有连接的孤立节点'
    },
    {
      id: 'no-cycles',
      name: '无循环依赖',
      type: 'flow',
      validator: validateNoCycles,
      enabled: true,
      priority: 2,
      description: '检查流程中是否存在循环依赖'
    },
    {
      id: 'start-node-required',
      name: '必须有开始节点',
      type: 'flow',
      validator: validateStartNodeRequired,
      enabled: true,
      priority: 3,
      description: '流程必须包含至少一个开始节点'
    },
    {
      id: 'end-node-required',
      name: '必须有结束节点',
      type: 'flow',
      validator: validateEndNodeRequired,
      enabled: true,
      priority: 4,
      description: '流程必须包含至少一个结束节点'
    },
    {
      id: 'node-label-required',
      name: '节点标签必填',
      type: 'node',
      validator: validateNodeLabelRequired,
      enabled: true,
      priority: 5,
      description: '所有节点必须有标签'
    },
    {
      id: 'decision-node-branches',
      name: '判断节点分支检查',
      type: 'node',
      validator: validateDecisionNodeBranches,
      enabled: true,
      priority: 6,
      description: '判断节点必须有至少两个输出分支'
    }
  ]

  const activeRules = ref<ValidationRule[]>([...builtInRules])

  // 计算属性
  const hasErrors = computed(() => 
    validationResults.value.some(result => result.type === 'error')
  )

  const hasWarnings = computed(() => 
    validationResults.value.some(result => result.type === 'warning')
  )

  const errorCount = computed(() => 
    validationResults.value.filter(result => result.type === 'error').length
  )

  const warningCount = computed(() => 
    validationResults.value.filter(result => result.type === 'warning').length
  )

  /**
   * 执行流程验证
   */
  const validateFlow = async (): Promise<ValidationResult[]> => {
    isValidating.value = true
    const results: ValidationResult[] = []

    try {
      const context: ValidationContext = {
        graph: null as any, // 这里需要传入实际的graph实例
        allNodes: flowStore.allNodes.value,
        allEdges: flowStore.allEdges.value,
        flowData: flowStore.exportData()
      }

      // 执行所有启用的规则
      for (const rule of activeRules.value.filter(r => r.enabled)) {
        try {
          const result = await rule.validator(context)
          if (result) {
            results.push(result)
          }
        } catch (error) {
          console.error(`Validation rule ${rule.id} failed:`, error)
          results.push({
            id: `${rule.id}-error`,
            ruleId: rule.id,
            type: 'error',
            message: `验证规则执行失败: ${error}`
          })
        }
      }

      // 按优先级排序
      results.sort((a, b) => {
        const ruleA = activeRules.value.find(r => r.id === a.ruleId)
        const ruleB = activeRules.value.find(r => r.id === b.ruleId)
        return (ruleA?.priority || 999) - (ruleB?.priority || 999)
      })

      validationResults.value = results
      flowStore.updateValidationResults(results)

      return results
    } finally {
      isValidating.value = false
    }
  }

  /**
   * 验证单个节点
   */
  const validateNode = async (node: FlowNode): Promise<ValidationResult[]> => {
    const results: ValidationResult[] = []
    const context: ValidationContext = {
      node,
      graph: null as any,
      allNodes: flowStore.allNodes.value,
      allEdges: flowStore.allEdges.value,
      flowData: flowStore.exportData()
    }

    for (const rule of activeRules.value.filter(r => r.enabled && r.type === 'node')) {
      try {
        const result = await rule.validator(context)
        if (result) {
          results.push(result)
        }
      } catch (error) {
        console.error(`Node validation rule ${rule.id} failed:`, error)
      }
    }

    return results
  }

  /**
   * 验证单个边
   */
  const validateEdge = async (edge: FlowEdge): Promise<ValidationResult[]> => {
    const results: ValidationResult[] = []
    const context: ValidationContext = {
      edge,
      graph: null as any,
      allNodes: flowStore.allNodes.value,
      allEdges: flowStore.allEdges.value,
      flowData: flowStore.exportData()
    }

    for (const rule of activeRules.value.filter(r => r.enabled && r.type === 'edge')) {
      try {
        const result = await rule.validator(context)
        if (result) {
          results.push(result)
        }
      } catch (error) {
        console.error(`Edge validation rule ${rule.id} failed:`, error)
      }
    }

    return results
  }

  /**
   * 清除验证结果
   */
  const clearValidation = () => {
    validationResults.value = []
    flowStore.updateValidationResults([])
  }

  /**
   * 添加自定义验证规则
   */
  const addRule = (rule: ValidationRule) => {
    activeRules.value.push(rule)
  }

  /**
   * 移除验证规则
   */
  const removeRule = (ruleId: string) => {
    const index = activeRules.value.findIndex(r => r.id === ruleId)
    if (index > -1) {
      activeRules.value.splice(index, 1)
    }
  }

  /**
   * 启用/禁用验证规则
   */
  const toggleRule = (ruleId: string, enabled: boolean) => {
    const rule = activeRules.value.find(r => r.id === ruleId)
    if (rule) {
      rule.enabled = enabled
    }
  }

  return {
    validationResults,
    isValidating,
    hasErrors,
    hasWarnings,
    errorCount,
    warningCount,
    activeRules,
    validateFlow,
    validateNode,
    validateEdge,
    clearValidation,
    addRule,
    removeRule,
    toggleRule
  }
}

// 验证规则实现

async function validateNoOrphanNodes(context: ValidationContext): Promise<ValidationResult | null> {
  const { allNodes, allEdges } = context
  const connectedNodes = new Set<string>()

  // 收集所有连接的节点
  allEdges.forEach(edge => {
    connectedNodes.add(edge.source)
    connectedNodes.add(edge.target)
  })

  // 查找孤立节点
  const orphanNodes = allNodes.filter(node => !connectedNodes.has(node.id))

  if (orphanNodes.length > 0) {
    return {
      id: 'orphan-nodes',
      ruleId: 'no-orphan-nodes',
      type: 'warning',
      message: `发现 ${orphanNodes.length} 个孤立节点: ${orphanNodes.map(n => n.label).join(', ')}`,
      suggestions: ['连接孤立节点到流程中', '删除不需要的节点']
    }
  }

  return null
}

async function validateNoCycles(context: ValidationContext): Promise<ValidationResult | null> {
  const { allEdges } = context

  if (detectCycle(allEdges)) {
    return {
      id: 'cycle-detected',
      ruleId: 'no-cycles',
      type: 'error',
      message: '流程中存在循环依赖',
      suggestions: ['检查节点连接', '移除形成循环的连线']
    }
  }

  return null
}

async function validateStartNodeRequired(context: ValidationContext): Promise<ValidationResult | null> {
  const { allNodes } = context
  const startNodes = allNodes.filter(node => node.type === 'start')

  if (startNodes.length === 0) {
    return {
      id: 'no-start-node',
      ruleId: 'start-node-required',
      type: 'error',
      message: '流程必须包含至少一个开始节点',
      suggestions: ['添加开始节点']
    }
  }

  return null
}

async function validateEndNodeRequired(context: ValidationContext): Promise<ValidationResult | null> {
  const { allNodes } = context
  const endNodes = allNodes.filter(node => node.type === 'end')

  if (endNodes.length === 0) {
    return {
      id: 'no-end-node',
      ruleId: 'end-node-required',
      type: 'error',
      message: '流程必须包含至少一个结束节点',
      suggestions: ['添加结束节点']
    }
  }

  return null
}

async function validateNodeLabelRequired(context: ValidationContext): Promise<ValidationResult | null> {
  const { node } = context
  if (!node) return null

  if (!node.label || node.label.trim() === '') {
    return {
      id: `node-${node.id}-no-label`,
      ruleId: 'node-label-required',
      type: 'warning',
      message: `节点 "${node.id}" 缺少标签`,
      target: {
        type: 'node',
        id: node.id
      },
      suggestions: ['为节点添加描述性标签']
    }
  }

  return null
}

async function validateDecisionNodeBranches(context: ValidationContext): Promise<ValidationResult | null> {
  const { node, allEdges } = context
  if (!node || node.type !== 'decision') return null

  const outgoingEdges = allEdges.filter(edge => edge.source === node.id)

  if (outgoingEdges.length < 2) {
    return {
      id: `decision-${node.id}-insufficient-branches`,
      ruleId: 'decision-node-branches',
      type: 'warning',
      message: `判断节点 "${node.label}" 应该有至少两个输出分支`,
      target: {
        type: 'node',
        id: node.id
      },
      suggestions: ['添加更多输出连线', '检查判断逻辑']
    }
  }

  return null
}
