<template>
  <div class="virtual-scroll">
    <div class="container" ref="container" :style="{ [containerProps.lengthProp]: `${props.showLength}px` }">
      <div class="drag-bar" ref="dragBar"
        :style="[barStyle, { [containerProps.lengthProp]: `${barLength}px`, position: 'absolute' }]">
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 虚拟滚动组件 可以选择滚动条的方向 纵向或横向
import { ref, onMounted, computed } from 'vue'
import { useDraggable, Position } from '@vueuse/core'
const props = defineProps({
  direction: {
    type: String,
    default: 'vertical',
  },
  totalLength: {
    type: Number,
    default: 0,
  },
  showLength: {
    type: Number,
    default: 0,
  },
  modelValue: {
    type: Number,
    default: 0,
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const container = ref<HTMLDivElement | null>(null)
const dragBar = ref<HTMLDivElement | null>(null)
const percentages = ref(0)

const { x, y, style: barStyle } = useDraggable(dragBar, {
  initialValue: { x: 0, y: 0 },
  containerElement: container,
  axis: props.direction === 'vertical' ? 'y' : 'x',
  onMove: (position: Position, event: PointerEvent) => {
    if (props.direction === 'vertical') {
      percentages.value = position.y / (props.showLength - barLength.value)
      debouncedUpdate(percentages.value)
    } else {
      percentages.value = position.x / (props.showLength - barLength.value)
      debouncedUpdate(percentages.value)
    }
  }
})

let timeout: any;
function debouncedUpdate(value: number) {
  clearTimeout(timeout)
  timeout = setTimeout(() => {
    percentages.value = value
    emits('change', percentages.value)
    emits('update:modelValue', percentages.value)
  }, 100)
}


const barLength = computed(() => {
  const minLength = props.showLength * 0.05 // 最小滚动条长度
  const res = (props.showLength / props.totalLength) * props.showLength
  return Math.max(minLength, res)
})

const containerProps = computed(() => {
  if (props.direction === 'vertical') {
    return {
      lengthProp: 'height',
      positionProp: 'top',
    }
  }
  else {
    return {
      lengthProp: 'width',
      positionProp: 'left',
    }
  }
})

function setBarPosition(value: number = 0.1) {
  const moveLength = (1 + value)
  if (props.direction === 'vertical') {

  }
}

</script>

<style lang="scss" scoped>
.virtual-scroll {
  overflow: hidden;

  .container {
    position: relative;
    background-color: red;
    height: 28px;
    width: 28px;

    .drag-bar {
      height: 100%;
      width: 100%;
      background-color: blue;
      cursor: pointer;
    }
  }
}
</style>