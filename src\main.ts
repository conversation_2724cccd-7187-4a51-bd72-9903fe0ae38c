import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";
import ArcoVue from "@arco-design/web-vue";
import { createPinia } from "pinia";
import "@arco-design/web-vue/dist/arco.css";
import VueKonva from "vue-konva";
import PrimeVue from "primevue/config";
import "primevue/resources/themes/aura-light-green/theme.css";
import "primeicons/primeicons.css";
import ganttastic from "@infectoone/vue-ganttastic";
import "@arco-design/web-vue/dist/arco.css";
const piania = createPinia();

const app = createApp(App);
app.use(ArcoVue);
app.use(piania);
app.use(PrimeVue);
app.use(VueKonva);
app.use(ganttastic);
app.mount("#app");
