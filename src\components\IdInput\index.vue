<template>
  <InputText
    ref="inputRef"
    :model-value="displayValue"
    v-bind="attrs"
    @change="handleChange"
    @update:model-value="handleUpdateModelValue"
    :class="['id-input', attrs.class]"
  >
    <!-- 转发所有插槽 -->
    <template v-for="(_, name) in slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}"></slot>
    </template>
  </InputText>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, useAttrs, useSlots } from "vue";
import InputText from "primevue/inputtext";

// ==================== 类型定义 ====================
interface IdInputProps {
  modelValue?: string;
  maxLength?: number;
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  invalid?: boolean;
  variant?: "filled" | "outlined";
  size?: "small" | "large";
}

interface IdInputEmits {
  (e: "update:modelValue", value: string): void;
  (e: "input", event: Event): void;
  (e: "change", event: Event): void;
  (e: "focus", event: FocusEvent): void;
  (e: "blur", event: FocusEvent): void;
  (e: "keydown", event: KeyboardEvent): void;
  (e: "keyup", event: KeyboardEvent): void;
  (e: "keypress", event: KeyboardEvent): void;
}

// ==================== Props & Emits ====================
const props = withDefaults(defineProps<IdInputProps>(), {
  modelValue: "",
  maxLength: undefined,
  placeholder: "",
  disabled: false,
  readonly: false,
  invalid: false,
  variant: "outlined",
  size: undefined,
});

const emit = defineEmits<IdInputEmits>();

// ==================== 使用 Composition API ====================
const attrs = useAttrs();
const slots = useSlots();

// ==================== 响应式数据 ====================
const inputRef = ref<InstanceType<typeof InputText>>();
const internalValue = ref(props.modelValue || "");

// ==================== 计算属性 ====================
const displayValue = computed(() => internalValue.value);

// ==================== 工具函数 ====================
/**
 * 获取输入框的DOM元素
 */
const getInputElement = (): HTMLInputElement | null => {
  // 尝试不同的方式获取DOM元素
  if (inputRef.value) {
    // 方式1: 直接访问$el
    if ((inputRef.value as any).$el) {
      return (inputRef.value as any).$el as HTMLInputElement;
    }
    // 方式2: 如果是直接的input元素
    if (inputRef.value instanceof HTMLInputElement) {
      return inputRef.value;
    }
  }
  return null;
};

/**
 * 过滤输入内容，只允许字母、数字、下划线、横线
 * 并将小写字母转换为大写
 */
const filterInput = (value: string): string => {
  return value
    .replace(/[^a-zA-Z0-9_-]/g, "") // 只保留字母、数字、下划线、横线
    .toUpperCase(); // 转换为大写
};

// ==================== 事件处理 ====================
const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const rawValue = target.value;
  const filteredValue = filterInput(rawValue);

  // 更新内部值
  internalValue.value = filteredValue;

  // 如果过滤后的值与原值不同，需要更新输入框显示
  if (filteredValue !== rawValue) {
    nextTick(() => {
      const inputElement = getInputElement();
      if (inputElement) {
        inputElement.value = filteredValue;
      }
    });
  }

  // 触发更新事件
  emit("update:modelValue", filteredValue);
  emit("change", event);
};

const handleUpdateModelValue = (value: string | undefined) => {
  const filteredValue = filterInput(value || "");
  internalValue.value = filteredValue;
  emit("update:modelValue", filteredValue);
};

// ==================== 监听器 ====================
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== internalValue.value) {
      internalValue.value = filterInput(newValue || "");
    }
  },
  { immediate: true }
);

// ==================== 暴露的方法 ====================
defineExpose({
  /**
   * 获取输入框的DOM元素
   */
  getInputElement,

  /**
   * 聚焦输入框
   */
  focus: () => {
    const input = getInputElement();
    input?.focus();
  },

  /**
   * 失焦输入框
   */
  blur: () => {
    const input = getInputElement();
    input?.blur();
  },

  /**
   * 选中所有文本
   */
  select: () => {
    const input = getInputElement();
    input?.select();
  },

  /**
   * 设置光标位置
   */
  setSelectionRange: (start: number, end: number) => {
    const input = getInputElement();
    input?.setSelectionRange(start, end);
  },
});
</script>

<style scoped>
/* ID输入框的自定义样式 */
.id-input {
  font-family: "Courier New", "Monaco", monospace;
  letter-spacing: 0.5px;
}

/* 确保输入框在各种状态下都有合适的样式 */
.id-input:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.id-input.p-invalid {
  border-color: #ef4444;
}

.id-input.p-invalid:focus {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
}

/* 禁用状态样式 */
.id-input:disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
}

/* 只读状态样式 */
.id-input[readonly] {
  background-color: #f9fafb;
  cursor: default;
}
</style>
