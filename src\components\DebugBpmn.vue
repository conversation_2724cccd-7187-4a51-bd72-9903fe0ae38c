<template>
  <div>
    <div class="containers">
      <Palette />
      <div v-show="isDebugMode" ref="debugBpmnContainer" class="bpmn-canvas"></div>
      <div v-show="!isDebugMode" ref="editBpmnContainer" class="bpmn-canvas"></div>
      <button @click="toggleMode('debug')" style="position: absolute; top: 20px; left: 300px;">debug模式</button>
      <button @click="toggleMode('edit')" style="position: absolute; top: 20px; left: 500px;">编辑模式</button>
      <div id="js-properties-panel" class="panel"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import BpmnModeler from 'bpmn-js/lib/Modeler';
import {
  BpmnPropertiesPanelModule,
  BpmnPropertiesProviderModule,
} from 'bpmn-js-properties-panel';
import DebugOverlayModule from '../debug-control-overlay/index.ts';
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda.json';
import { xmlStr3, xmlStr2 } from './data/xmlStr';
import Palette from './palette/index.vue'
import './style/index.scss'

const debugBpmnContainer: any = ref(null)
const editBpmnContainer: any = ref(null)

const isDebugMode = ref(false)
const debugModeler: any = ref(null)
const editModeler: any = ref(null)
let diagramXML = xmlStr3

// 只读模式配置
const readonlyMode = {
  paletteProvider: ["value", ''],//禁用/清空左侧工具栏
  labelEditingProvider: ["value", ''],//禁用节点编辑
  contextPadProvider: ["value", ''],//禁用图形菜单
  bendpoints: ["value", {}],//禁用连线拖动
  // zoomScroll: ["value", ''],//禁用滚动
  // moveCanvas:['value',''],//禁用拖动整个流程图
  move: ['value', '']//禁用单个图形拖动
}


async function loadBpmnDiagram() {
  if (isDebugMode.value) {
    await debugModeler.value.importXML(diagramXML);
  } else {
    await editModeler.value.importXML(diagramXML);
  }
}

async function toggleMode(mode: string) {
  const newMode = mode === 'debug'
  // 判断模式是否变化，如果没有变化，则直接返回
  if (newMode === isDebugMode.value) {
    return
  }
  // 如果原本是编辑模式，则需要保存当前的xml 并且在debug模式下加载xml
  if (!isDebugMode.value) {
    const { xml } = await editModeler.value.saveXML();
    diagramXML = xml
  }
  isDebugMode.value = newMode
  await loadBpmnDiagram()
}

/**
 * 初始化debug模式,只读模式,禁用右键菜单,禁用滚动,禁用拖动整个流程图,禁用单个图形拖动
 * 鼠标悬浮到节点上,显示断点图标,点击图标,发送post请求,设置断点，并且显示断点图标
 * 后端获取消息,高亮暂停执行的debug节点,并显示日志信息 
*/
function initDebugMode() {
  // 初始化Modeler
  debugModeler.value = new BpmnModeler({
    container: debugBpmnContainer.value,
    propertiesPanel: {
      parent: '#js-properties-panel'
    },
    additionalModules: [readonlyMode, DebugOverlayModule],
  });
  // 使用overlays模块显示断点图标
  // 设置悬浮事件
  const eventBus = debugModeler.value.get('eventBus');
  eventBus.on('element.hover', (event: any) => {
    const element = event.element;
    console.log('%c [ hover ]-93', 'font-size:13px; background:pink; color:#bf2c9f;', element)
    const overlays = debugModeler.value.get('overlays');
    console.log('%c [ overlays ]-91', 'font-size:13px; background:pink; color:#bf2c9f;', overlays)
    overlays.add(element, 'break-point', {
      position: {
        bottom: 0,
        right: 0
      },
      html: '<div class="break-point">123456</div>',
    });
  });
  // 设置移出事件
  eventBus.on('element.out', (event: any) => {
    const element = event.element;
    console.log('%c [ out ]-113', 'font-size:13px; background:pink; color:#bf2c9f;', element)
  });
}

function initEditMode() {
  editModeler.value = new BpmnModeler({
    container: editBpmnContainer.value,
    propertiesPanel: {
      parent: '#js-properties-panel'
    },
    additionalModules: [BpmnPropertiesPanelModule, BpmnPropertiesProviderModule],
    //moddleExtensions的作用是添加额外的模型 例如camunda的模型 用于扩展bpmn的模型
    moddleExtensions: {
      camunda: camundaModdleDescriptor
    },
  })
  // 设置点击事件
  const eventBus = editModeler.value.get('eventBus');
  eventBus.on('element.click', (event: any) => {
    const element = event.element;
    console.log('%c [ element ]-130', 'font-size:13px; background:pink; color:#bf2c9f;', element)
  });
}

// 初始化两个实例
onMounted(() => {
  initEditMode()
  initDebugMode();
  loadBpmnDiagram();
});

</script>

<style lang="scss" scoped>
.containers {
  position: absolute;
  /* background-color: #1f1a1a; */
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.bpmn-canvas {
  width: 100%;
  height: 100%;
}

#js-properties-panel {
  width: 300px;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  background-color: #f0f0f0;
  border-left: 1px solid #ccc;
  overflow: auto;
  z-index: 100;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  box-sizing: border-box;
}
</style>