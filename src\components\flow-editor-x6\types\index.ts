import type { Node, Edge, Graph } from '@antv/x6'

// 基础类型定义
export interface FlowPosition {
  x: number
  y: number
}

export interface FlowSize {
  width: number
  height: number
}

// 节点类型定义
export interface FlowNodeType {
  id: string
  name: string
  category: string
  icon?: string
  color?: string
  description?: string
  defaultSize?: FlowSize
  defaultProps?: Record<string, any>
  validationRules?: ValidationRule[]
  customComponent?: string
  allowedConnections?: {
    input?: string[]
    output?: string[]
  }
}

// 节点数据定义
export interface FlowNode {
  id: string
  type: string
  label: string
  position: FlowPosition
  size: FlowSize
  data: Record<string, any>
  style?: Record<string, any>
  ports?: FlowPort[]
  validationState?: ValidationState
  metadata?: Record<string, any>
}

// 连线数据定义
export interface FlowEdge {
  id: string
  source: string
  target: string
  sourcePort?: string
  targetPort?: string
  label?: string
  data: Record<string, any>
  style?: Record<string, any>
  validationState?: ValidationState
  metadata?: Record<string, any>
}

// 端口定义
export interface FlowPort {
  id: string
  group: 'input' | 'output'
  position: FlowPosition
  label?: string
  connected?: boolean
  allowMultiple?: boolean
  dataType?: string
}

// 验证相关类型
export interface ValidationRule {
  id: string
  name: string
  type: 'node' | 'edge' | 'flow'
  validator: (context: ValidationContext) => ValidationResult | Promise<ValidationResult>
  enabled: boolean
  priority: number
  description?: string
}

export interface ValidationContext {
  node?: FlowNode
  edge?: FlowEdge
  graph: Graph
  allNodes: FlowNode[]
  allEdges: FlowEdge[]
  flowData: FlowData
}

export interface ValidationResult {
  id: string
  ruleId: string
  type: 'error' | 'warning' | 'info'
  message: string
  target?: {
    type: 'node' | 'edge'
    id: string
  }
  position?: FlowPosition
  suggestions?: string[]
}

export type ValidationState = 'valid' | 'invalid' | 'warning' | 'pending'

// 流程数据定义
export interface FlowData {
  nodes: FlowNode[]
  edges: FlowEdge[]
  metadata: {
    version: string
    createdAt: string
    updatedAt: string
    author?: string
    description?: string
    tags?: string[]
  }
  config: FlowConfig
}

// 配置定义
export interface FlowConfig {
  grid: {
    enabled: boolean
    size: number
    visible: boolean
  }
  snap: {
    enabled: boolean
    tolerance: number
  }
  zoom: {
    min: number
    max: number
    step: number
  }
  selection: {
    enabled: boolean
    multiple: boolean
  }
  connection: {
    allowBlank: boolean
    allowMulti: boolean
    allowLoop: boolean
  }
  validation: {
    enabled: boolean
    realtime: boolean
    rules: string[]
  }
}

// 拖拽相关类型
export interface DragData {
  type: 'node' | 'edge'
  nodeType?: string
  data?: Record<string, any>
  source?: any
}

// 历史记录类型
export interface HistoryRecord {
  id: string
  timestamp: number
  action: 'add' | 'update' | 'delete' | 'move'
  target: {
    type: 'node' | 'edge'
    id: string
  }
  before?: any
  after?: any
}

// Store 状态类型
export interface FlowStoreState {
  nodes: Map<string, FlowNode>
  edges: Map<string, FlowEdge>
  selectedNodes: Set<string>
  selectedEdges: Set<string>
  clipboard: {
    nodes: FlowNode[]
    edges: FlowEdge[]
  }
  history: HistoryRecord[]
  historyIndex: number
  config: FlowConfig
  validationResults: ValidationResult[]
  isDirty: boolean
}

// 事件类型
export interface FlowEvents {
  'node:added': (node: FlowNode) => void
  'node:removed': (nodeId: string) => void
  'node:updated': (node: FlowNode, changes: Partial<FlowNode>) => void
  'node:selected': (nodeId: string) => void
  'node:deselected': (nodeId: string) => void
  'edge:added': (edge: FlowEdge) => void
  'edge:removed': (edgeId: string) => void
  'edge:updated': (edge: FlowEdge, changes: Partial<FlowEdge>) => void
  'edge:selected': (edgeId: string) => void
  'edge:deselected': (edgeId: string) => void
  'validation:changed': (results: ValidationResult[]) => void
  'data:changed': (data: FlowData) => void
}

// 工具函数类型
export type NodeFactory = (type: string, position: FlowPosition, data?: Record<string, any>) => FlowNode
export type EdgeFactory = (source: string, target: string, data?: Record<string, any>) => FlowEdge
export type Validator = (context: ValidationContext) => ValidationResult | Promise<ValidationResult>

// 插件接口
export interface FlowPlugin {
  name: string
  version: string
  install: (store: any, options?: any) => void
  uninstall?: () => void
}

// 导出/导入类型
export interface ExportOptions {
  format: 'json' | 'xml' | 'svg' | 'png'
  includeMetadata?: boolean
  includeValidation?: boolean
}

export interface ImportOptions {
  format: 'json' | 'xml'
  merge?: boolean
  validateOnImport?: boolean
}
