// @ts-nocheck
// 点击使整个视图锁定 无法移动 modeler.value.get可以获取到: elementRegistry、elementFactory、moddle、canvas、selection、commandStack、modeling、palette、propertiesPanel、pathMap、globalConnect、contextPad、paletteProvider、labelEditingProvider、contextPadProvider、bendpoints、zoomScroll、moveCanvas、move   等对象  
// moddle对象的作用是: 用于获取bpmn的模型信息
// modeling对象的作用是: 用于对bpmn的模型进行操作
// modeler.get("create") 可以获取到创建节点的方法 作用是: 用于创建节点 例如: modeler.get("create").appendShape(event.element, {type: 'bpmn:Task', x: 100, y: 100, width: 80, height: 80})
// modeler.get("elementRegistry") 可以获取到所有的节点信息
// modeler.get("elementFactory") 可以获取到创建节点的工厂 作用是: 用于创建节点 例如: modeler.get("elementFactory").createShape({type: 'bpmn:Task', x: 100, y: 100, width: 80, height: 80})
// create和elementFactory的区别是: create是直接创建节点并且添加到画布上 elementFactory是创建节点但是不会添加到画布上 
// 需要手动添加 例如: modeler.get("elementFactory").createShape({type: 'bpmn:Task', x: 100, y: 100, width: 80, height: 80})
// get('overlays') 可以获取到所有的覆盖物 例如: modeler.get('overlays').add('elementId', {position: {bottom: 0, right: 0}, html: '<div>123</div>'})
// 这里的type是指: 节点的类型 例如: bpmn:Task、bpmn:StartEvent、bpmn:EndEvent、bpmn:ExclusiveGateway、bpmn:ParallelGateway、bpmn:InclusiveGateway、bpmn:ComplexGateway、bpmn:UserTask、bpmn:ServiceTask、bpmn:ScriptTask、bpmn:CallActivity、bpmn:SubProcess、bpmn:Participant、bpmn:Lane、bpmn:DataObject、bpmn:DataStoreReference、bpmn:SequenceFlow、bpmn:MessageFlow、bpmn:Association、bpmn:TextAnnotation、bpmn:Group
// 节点类型的定义是来源于bpmn规范的 例如: bpmn:Task节点是一个任务节点 bpmn:StartEvent节点是一个开始节点  bpmn:EndEvent节点是一个结束节点  bpmn:ExclusiveGateway节点是一个排他网关  bpmn:ParallelGateway节点是一个并行网关  bpmn:InclusiveGateway节点是一个包容网关  bpmn:ComplexGateway节点是一个复杂网关  bpmn:UserTask节点是一个用户任务  bpmn:ServiceTask节点是一个服务任务  bpmn:ScriptTask节点是一个脚本任务  bpmn:CallActivity节点是一个调用活动  bpmn:SubProcess节点是一个子流程  bpmn:Participant节点是一个参与者  bpmn:Lane节点是一个泳道  bpmn:DataObject节点是一个数据对象  bpmn:DataStoreReference节点是一个数据存储引用  bpmn:SequenceFlow节点是一个顺序流  bpmn:MessageFlow节点是一个消息流  bpmn:Association节点是一个关联  bpmn:TextAnnotation节点是一个文本注释  bpmn:Group节点是一个组
// 开始节点的作用是: 用于标识流程的开
// 在bpmn中节点之间的连线是通过SequenceFlow来连接的 例如: modeler.get("elementFactory").createConnection({type: 'bpmn:SequenceFlow', source: event.element, target: event.element})
// 在tokenSimulationPlugin中有一个toggleMode方法 作用是: 切换流程的执行模式
// 在执行模式下 流程是不可编辑的 并且开始节点会有可执行的按钮
// 该按钮是在bpmn-js-token-simulation中添加的


// 实现功能: 点击节点 则节点变为激活状态， 在节点左下角出现一个可执行的按钮
// 实现思路: 通过modeling对象的setColor方法改变节点的颜色 通过modeling对象的appendShape方法在节点的左下角添加一个按钮
// 实现步骤:
// 1. 获取modeling对象
const modeling = modeler.value.get('modeling');
// 2. 获取elementRegistry对象 该对象中存储了所有的节点信息 包括节点的位置、大小、类型、id等信息 如果要获取某个节点的信息 可以通过id来获取 比如: elementRegistry.get(id)
const elementRegistry = modeler.value.get('elementRegistry');
// 3. 获取elementFactory对象
const elementFactory = modeler.value.get('elementFactory');
// 4. 获取当前点击的节点
const element = event.element;
// 5. 获取当前点击的节点的位置
const x = element.x;
const y = element.y;
// 6. 获取当前点击的节点的宽度
const width = element.width;
// 7. 获取当前点击的节点的高度
const height = element.height;
// 8. 获取当前点击的节点的类型
const type = element.type;
// 9. 获取当前点击的节点的id
const id = element.id;
// 10. 进行添加
// 10.1 添加一个类似播放按钮的按钮
const execBtn = elementFactory.createShape({
    type: 'bpmn:Task',
    width: 20,
    height: 20,
    stroke: 'green',
    fill: 'green',
    id: 'execBtn12312',
    businessObject: {
        type: 'bpmn:Task',
        id: 'execBtn',
        name: '执行',
    },
});
// 10.2 将按钮添加到画布上
const canvas = modeler.value.get('canvas');
// canvas.addShape(execBtn, element);
// 或者使用create进行添加 create 对象中的方法有: start、 appendShape、 appendFlow、 connect、 createShape、 createLine、 createLabel、 createConnection、 createParticipant、 createLane、 createTextAnnotation、 createGroup
// create对象中的方法的作用是: 用于创建节点、连线、标签、连接、参与者、泳道、文本注释、组
const create = modeler.value.get('create');