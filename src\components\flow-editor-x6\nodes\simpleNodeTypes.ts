import type { FlowNodeType } from '../types'

/**
 * 简化的节点类型定义
 */
export const SIMPLE_NODE_TYPES: FlowNodeType[] = [
  {
    id: 'start',
    name: '开始',
    category: 'control',
    icon: 'pi pi-play',
    color: '#4CAF50',
    description: '流程开始节点',
    defaultSize: { width: 100, height: 60 },
    defaultProps: {
      style: {
        borderRadius: 30,
        fill: '#4CAF50',
        stroke: '#388E3C',
        strokeWidth: 2
      }
    },
    allowedConnections: {
      input: [],
      output: ['*']
    },
    validationRules: []
  },
  {
    id: 'end',
    name: '结束',
    category: 'control',
    icon: 'pi pi-stop',
    color: '#F44336',
    description: '流程结束节点',
    defaultSize: { width: 100, height: 60 },
    defaultProps: {
      style: {
        borderRadius: 30,
        fill: '#F44336',
        stroke: '#D32F2F',
        strokeWidth: 2
      }
    },
    allowedConnections: {
      input: ['*'],
      output: []
    },
    validationRules: []
  },
  {
    id: 'process',
    name: '处理',
    category: 'process',
    icon: 'pi pi-cog',
    color: '#2196F3',
    description: '业务处理节点',
    defaultSize: { width: 120, height: 80 },
    defaultProps: {
      style: {
        borderRadius: 8,
        fill: '#2196F3',
        stroke: '#1976D2',
        strokeWidth: 2
      },
      config: {
        timeout: 30000,
        retryCount: 3
      }
    },
    allowedConnections: {
      input: ['*'],
      output: ['*']
    },
    validationRules: []
  },
  {
    id: 'decision',
    name: '判断',
    category: 'control',
    icon: 'pi pi-question',
    color: '#FF9800',
    description: '条件判断节点',
    defaultSize: { width: 100, height: 100 },
    defaultProps: {
      style: {
        fill: '#FF9800',
        stroke: '#F57C00',
        strokeWidth: 2
      },
      conditions: []
    },
    allowedConnections: {
      input: ['*'],
      output: ['*']
    },
    validationRules: []
  },
  {
    id: 'parallel',
    name: '并行',
    category: 'control',
    icon: 'pi pi-clone',
    color: '#9C27B0',
    description: '并行处理节点',
    defaultSize: { width: 120, height: 60 },
    defaultProps: {
      style: {
        borderRadius: 4,
        fill: '#9C27B0',
        stroke: '#7B1FA2',
        strokeWidth: 2
      },
      maxConcurrency: 5
    },
    allowedConnections: {
      input: ['*'],
      output: ['*']
    },
    validationRules: []
  },
  {
    id: 'api',
    name: 'API调用',
    category: 'integration',
    icon: 'pi pi-send',
    color: '#E91E63',
    description: 'API接口调用节点',
    defaultSize: { width: 120, height: 80 },
    defaultProps: {
      style: {
        borderRadius: 8,
        fill: '#E91E63',
        stroke: '#C2185B',
        strokeWidth: 2
      },
      method: 'GET',
      url: '',
      headers: {},
      timeout: 30000
    },
    allowedConnections: {
      input: ['*'],
      output: ['*']
    },
    validationRules: []
  },
  {
    id: 'database',
    name: '数据库',
    category: 'data',
    icon: 'pi pi-database',
    color: '#3F51B5',
    description: '数据库操作节点',
    defaultSize: { width: 120, height: 80 },
    defaultProps: {
      style: {
        borderRadius: 8,
        fill: '#3F51B5',
        stroke: '#303F9F',
        strokeWidth: 2
      },
      operation: 'select',
      table: '',
      query: ''
    },
    allowedConnections: {
      input: ['*'],
      output: ['*']
    },
    validationRules: []
  },
  {
    id: 'notification',
    name: '通知',
    category: 'integration',
    icon: 'pi pi-bell',
    color: '#FF5722',
    description: '消息通知节点',
    defaultSize: { width: 120, height: 80 },
    defaultProps: {
      style: {
        borderRadius: 8,
        fill: '#FF5722',
        stroke: '#D84315',
        strokeWidth: 2
      },
      type: 'email',
      recipients: [],
      subject: '',
      content: ''
    },
    allowedConnections: {
      input: ['*'],
      output: ['*']
    },
    validationRules: []
  }
]

/**
 * 简单的节点类型注册表
 */
export class SimpleNodeTypeRegistry {
  private types = new Map<string, FlowNodeType>()

  constructor() {
    // 注册基础节点类型
    SIMPLE_NODE_TYPES.forEach(type => {
      this.types.set(type.id, type)
    })
  }

  /**
   * 获取节点类型
   */
  get(typeId: string): FlowNodeType | undefined {
    return this.types.get(typeId)
  }

  /**
   * 获取所有节点类型
   */
  getAll(): FlowNodeType[] {
    return Array.from(this.types.values())
  }

  /**
   * 按分类获取节点类型
   */
  getByCategory(category: string): FlowNodeType[] {
    return this.getAll().filter(type => type.category === category)
  }

  /**
   * 获取所有分类
   */
  getCategories(): string[] {
    const categories = new Set<string>()
    this.types.forEach(type => categories.add(type.category))
    return Array.from(categories)
  }

  /**
   * 搜索节点类型
   */
  search(query: string): FlowNodeType[] {
    const lowerQuery = query.toLowerCase()
    return this.getAll().filter(type =>
      type.name.toLowerCase().includes(lowerQuery) ||
      type.description?.toLowerCase().includes(lowerQuery) ||
      type.category.toLowerCase().includes(lowerQuery)
    )
  }

  /**
   * 检查节点类型是否存在
   */
  has(typeId: string): boolean {
    return this.types.has(typeId)
  }

  /**
   * 注册节点类型
   */
  register(nodeType: FlowNodeType): void {
    this.types.set(nodeType.id, { ...nodeType })
  }

  /**
   * 注销节点类型
   */
  unregister(typeId: string): void {
    this.types.delete(typeId)
  }
}

// 全局节点类型注册表实例
export const nodeTypeRegistry = new SimpleNodeTypeRegistry()
