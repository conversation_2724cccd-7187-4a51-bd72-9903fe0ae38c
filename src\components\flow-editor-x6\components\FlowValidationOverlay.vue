<template>
  <div class="validation-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div
            :class="[
              'flex items-center justify-center w-10 h-10 rounded-full',
              hasErrors ? 'bg-red-100 text-red-600' : 'bg-yellow-100 text-yellow-600'
            ]"
          >
            <i :class="hasErrors ? 'pi pi-times-circle' : 'pi pi-exclamation-triangle'" class="text-xl" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">验证结果</h3>
            <p class="text-sm text-gray-500">
              发现 {{ errorCount }} 个错误，{{ warningCount }} 个警告
            </p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <i class="pi pi-times text-gray-500" />
        </button>
      </div>

      <!-- 统计信息 -->
      <div class="p-4 bg-gray-50 border-b border-gray-200">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
            <span class="text-sm text-gray-700">错误: {{ errorCount }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span class="text-sm text-gray-700">警告: {{ warningCount }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span class="text-sm text-gray-700">信息: {{ infoCount }}</span>
          </div>
        </div>
      </div>

      <!-- 结果列表 -->
      <div class="flex-1 overflow-y-auto">
        <div class="p-4 space-y-3">
          <div
            v-for="result in sortedResults"
            :key="result.id"
            :class="[
              'border rounded-lg p-4 cursor-pointer transition-all duration-200',
              getResultBorderClass(result.type),
              'hover:shadow-md'
            ]"
            @click="handleResultClick(result)"
          >
            <!-- 结果头部 -->
            <div class="flex items-start space-x-3">
              <div
                :class="[
                  'flex items-center justify-center w-6 h-6 rounded-full flex-shrink-0 mt-0.5',
                  getResultIconClass(result.type)
                ]"
              >
                <i :class="getResultIcon(result.type)" class="text-sm" />
              </div>
              
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <h4 :class="['font-medium', getResultTextClass(result.type)]">
                    {{ getResultTitle(result) }}
                  </h4>
                  <span class="text-xs text-gray-500">
                    {{ getRuleName(result.ruleId) }}
                  </span>
                </div>
                
                <p class="text-sm text-gray-700 mt-1">{{ result.message }}</p>
                
                <!-- 目标信息 -->
                <div v-if="result.target" class="flex items-center space-x-2 mt-2">
                  <span class="text-xs text-gray-500">目标:</span>
                  <span class="text-xs bg-gray-100 px-2 py-1 rounded">
                    {{ result.target.type }}: {{ result.target.id }}
                  </span>
                </div>
                
                <!-- 建议 -->
                <div v-if="result.suggestions && result.suggestions.length > 0" class="mt-3">
                  <p class="text-xs text-gray-600 mb-1">建议:</p>
                  <ul class="text-xs text-gray-600 space-y-1">
                    <li
                      v-for="suggestion in result.suggestions"
                      :key="suggestion"
                      class="flex items-start space-x-1"
                    >
                      <span class="text-gray-400">•</span>
                      <span>{{ suggestion }}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-2">
          <button
            @click="exportResults"
            class="px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            <i class="pi pi-download mr-2" />
            导出结果
          </button>
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            关闭
          </button>
          <button
            v-if="hasErrors"
            @click="fixAllErrors"
            class="px-4 py-2 text-sm text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
          >
            尝试修复错误
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ValidationResult } from '../types'

// Props
interface Props {
  results: ValidationResult[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
  'result-click': [result: ValidationResult]
  'fix-error': [result: ValidationResult]
}>()

// 计算属性
const errorCount = computed(() => 
  props.results.filter(r => r.type === 'error').length
)

const warningCount = computed(() => 
  props.results.filter(r => r.type === 'warning').length
)

const infoCount = computed(() => 
  props.results.filter(r => r.type === 'info').length
)

const hasErrors = computed(() => errorCount.value > 0)

const sortedResults = computed(() => {
  return [...props.results].sort((a, b) => {
    // 按类型排序：error > warning > info
    const typeOrder = { error: 0, warning: 1, info: 2 }
    return typeOrder[a.type] - typeOrder[b.type]
  })
})

// 方法
const getResultBorderClass = (type: string): string => {
  switch (type) {
    case 'error':
      return 'border-red-200 bg-red-50'
    case 'warning':
      return 'border-yellow-200 bg-yellow-50'
    case 'info':
      return 'border-blue-200 bg-blue-50'
    default:
      return 'border-gray-200 bg-gray-50'
  }
}

const getResultIconClass = (type: string): string => {
  switch (type) {
    case 'error':
      return 'bg-red-100 text-red-600'
    case 'warning':
      return 'bg-yellow-100 text-yellow-600'
    case 'info':
      return 'bg-blue-100 text-blue-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

const getResultTextClass = (type: string): string => {
  switch (type) {
    case 'error':
      return 'text-red-800'
    case 'warning':
      return 'text-yellow-800'
    case 'info':
      return 'text-blue-800'
    default:
      return 'text-gray-800'
  }
}

const getResultIcon = (type: string): string => {
  switch (type) {
    case 'error':
      return 'pi pi-times'
    case 'warning':
      return 'pi pi-exclamation-triangle'
    case 'info':
      return 'pi pi-info-circle'
    default:
      return 'pi pi-circle'
  }
}

const getResultTitle = (result: ValidationResult): string => {
  switch (result.type) {
    case 'error':
      return '错误'
    case 'warning':
      return '警告'
    case 'info':
      return '信息'
    default:
      return '未知'
  }
}

const getRuleName = (ruleId: string): string => {
  // 这里可以从规则注册表获取规则名称
  const ruleNames: Record<string, string> = {
    'no-orphan-nodes': '无孤立节点',
    'no-cycles': '无循环依赖',
    'start-node-required': '必须有开始节点',
    'end-node-required': '必须有结束节点',
    'node-label-required': '节点标签必填',
    'decision-node-branches': '判断节点分支检查'
  }
  return ruleNames[ruleId] || ruleId
}

const handleResultClick = (result: ValidationResult) => {
  emit('result-click', result)
}

const fixAllErrors = () => {
  const errors = props.results.filter(r => r.type === 'error')
  errors.forEach(error => {
    emit('fix-error', error)
  })
}

const exportResults = () => {
  const data = {
    timestamp: new Date().toISOString(),
    summary: {
      total: props.results.length,
      errors: errorCount.value,
      warnings: warningCount.value,
      info: infoCount.value
    },
    results: props.results
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `validation-results-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
.validation-overlay {
  backdrop-filter: blur(4px);
}

/* 动画效果 */
.validation-overlay {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.validation-overlay > div {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
