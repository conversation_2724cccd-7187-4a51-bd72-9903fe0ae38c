<template>
  <div class="flow-property-panel h-full flex flex-col bg-gray-50">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
      <h3 class="text-lg font-semibold text-gray-800">属性面板</h3>
      <button
        @click="toggleCollapsed"
        class="p-1 rounded hover:bg-gray-100"
      >
        <i :class="collapsed ? 'pi pi-angle-left' : 'pi pi-angle-right'" />
      </button>
    </div>

    <!-- 内容区域 -->
    <div v-if="!collapsed" class="flex-1 overflow-y-auto">
      <!-- 无选择状态 -->
      <div v-if="!selectedNode && !selectedEdge" class="p-6 text-center text-gray-500">
        <i class="pi pi-info-circle text-4xl mb-4" />
        <p>选择节点或连线查看属性</p>
      </div>

      <!-- 节点属性 -->
      <div v-else-if="selectedNode" class="p-4 space-y-4">
        <!-- 基本信息 -->
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <h4 class="font-medium text-gray-800 mb-3 flex items-center">
            <i class="pi pi-info-circle mr-2" />
            基本信息
          </h4>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">节点ID</label>
              <input
                :value="selectedNode.id"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">节点类型</label>
              <input
                :value="selectedNode.type"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">标签</label>
              <input
                v-model="nodeForm.label"
                @input="updateNodeProperty('label', nodeForm.label)"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                placeholder="输入节点标签"
              />
            </div>
          </div>
        </div>

        <!-- 位置和大小 -->
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <h4 class="font-medium text-gray-800 mb-3 flex items-center">
            <i class="pi pi-arrows-alt mr-2" />
            位置和大小
          </h4>
          
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">X坐标</label>
              <input
                v-model.number="nodeForm.position.x"
                @input="updateNodeProperty('position', nodeForm.position)"
                type="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Y坐标</label>
              <input
                v-model.number="nodeForm.position.y"
                @input="updateNodeProperty('position', nodeForm.position)"
                type="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">宽度</label>
              <input
                v-model.number="nodeForm.size.width"
                @input="updateNodeProperty('size', nodeForm.size)"
                type="number"
                min="50"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">高度</label>
              <input
                v-model.number="nodeForm.size.height"
                @input="updateNodeProperty('size', nodeForm.size)"
                type="number"
                min="30"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        <!-- 样式设置 -->
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <h4 class="font-medium text-gray-800 mb-3 flex items-center">
            <i class="pi pi-palette mr-2" />
            样式设置
          </h4>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">填充颜色</label>
              <div class="flex items-center space-x-2">
                <input
                  v-model="nodeForm.style.fill"
                  @input="updateNodeProperty('style', nodeForm.style)"
                  type="color"
                  class="w-12 h-8 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  v-model="nodeForm.style.fill"
                  @input="updateNodeProperty('style', nodeForm.style)"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="#ffffff"
                />
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">边框颜色</label>
              <div class="flex items-center space-x-2">
                <input
                  v-model="nodeForm.style.stroke"
                  @input="updateNodeProperty('style', nodeForm.style)"
                  type="color"
                  class="w-12 h-8 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  v-model="nodeForm.style.stroke"
                  @input="updateNodeProperty('style', nodeForm.style)"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="#333333"
                />
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">边框宽度</label>
              <input
                v-model.number="nodeForm.style.strokeWidth"
                @input="updateNodeProperty('style', nodeForm.style)"
                type="number"
                min="0"
                max="10"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        <!-- 自定义数据 -->
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <h4 class="font-medium text-gray-800 mb-3 flex items-center">
            <i class="pi pi-database mr-2" />
            自定义数据
          </h4>
          
          <div class="space-y-3">
            <div
              v-for="(value, key) in nodeForm.data"
              :key="key"
              class="flex items-center space-x-2"
            >
              <input
                :value="key"
                @input="updateDataKey($event, key, value)"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                placeholder="键名"
              />
              <input
                :value="value"
                @input="updateDataValue(key, $event)"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                placeholder="值"
              />
              <button
                @click="removeDataField(key)"
                class="p-2 text-red-500 hover:bg-red-50 rounded"
              >
                <i class="pi pi-times" />
              </button>
            </div>
            
            <button
              @click="addDataField"
              class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-blue-400 hover:text-blue-600 transition-colors"
            >
              <i class="pi pi-plus mr-2" />
              添加字段
            </button>
          </div>
        </div>
      </div>

      <!-- 边属性 -->
      <div v-else-if="selectedEdge" class="p-4 space-y-4">
        <!-- 基本信息 -->
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <h4 class="font-medium text-gray-800 mb-3 flex items-center">
            <i class="pi pi-info-circle mr-2" />
            连线信息
          </h4>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">连线ID</label>
              <input
                :value="selectedEdge.id"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">源节点</label>
              <input
                :value="selectedEdge.source"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">目标节点</label>
              <input
                :value="selectedEdge.target"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">标签</label>
              <input
                v-model="edgeForm.label"
                @input="updateEdgeProperty('label', edgeForm.label)"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                placeholder="输入连线标签"
              />
            </div>
          </div>
        </div>

        <!-- 样式设置 -->
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <h4 class="font-medium text-gray-800 mb-3 flex items-center">
            <i class="pi pi-palette mr-2" />
            连线样式
          </h4>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">线条颜色</label>
              <div class="flex items-center space-x-2">
                <input
                  v-model="edgeForm.style.stroke"
                  @input="updateEdgeProperty('style', edgeForm.style)"
                  type="color"
                  class="w-12 h-8 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  v-model="edgeForm.style.stroke"
                  @input="updateEdgeProperty('style', edgeForm.style)"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  placeholder="#333333"
                />
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">线条宽度</label>
              <input
                v-model.number="edgeForm.style.strokeWidth"
                @input="updateEdgeProperty('style', edgeForm.style)"
                type="number"
                min="1"
                max="10"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 折叠状态 -->
    <div v-if="collapsed" class="flex-1 flex items-center justify-center">
      <button
        @click="collapsed = false"
        class="p-3 rounded-lg hover:bg-gray-100 transition-colors"
        title="展开属性面板"
      >
        <i class="pi pi-cog text-xl text-gray-600" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { FlowNode, FlowEdge } from '../types'
import { debounce } from '../utils/common'

// Props
interface Props {
  selectedNode?: FlowNode | null
  selectedEdge?: FlowEdge | null
}

const props = withDefaults(defineProps<Props>(), {
  selectedNode: null,
  selectedEdge: null
})

// Emits
const emit = defineEmits<{
  'update-node': [data: Partial<FlowNode>]
  'update-edge': [data: Partial<FlowEdge>]
}>()

// 响应式数据
const collapsed = ref(false)
const nodeForm = ref({
  label: '',
  position: { x: 0, y: 0 },
  size: { width: 120, height: 60 },
  style: {
    fill: '#ffffff',
    stroke: '#333333',
    strokeWidth: 2
  },
  data: {} as Record<string, any>
})

const edgeForm = ref({
  label: '',
  style: {
    stroke: '#333333',
    strokeWidth: 2
  }
})

// 防抖更新函数
const debouncedUpdateNode = debounce((property: string, value: any) => {
  emit('update-node', { [property]: value })
}, 300)

const debouncedUpdateEdge = debounce((property: string, value: any) => {
  emit('update-edge', { [property]: value })
}, 300)

// 方法
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

const updateNodeProperty = (property: string, value: any) => {
  debouncedUpdateNode(property, value)
}

const updateEdgeProperty = (property: string, value: any) => {
  debouncedUpdateEdge(property, value)
}

const addDataField = () => {
  const key = `field_${Date.now()}`
  nodeForm.value.data[key] = ''
  updateNodeProperty('data', nodeForm.value.data)
}

const removeDataField = (key: string) => {
  delete nodeForm.value.data[key]
  updateNodeProperty('data', nodeForm.value.data)
}

const updateDataKey = (event: Event, oldKey: string, value: any) => {
  const newKey = (event.target as HTMLInputElement).value
  if (newKey !== oldKey) {
    delete nodeForm.value.data[oldKey]
    nodeForm.value.data[newKey] = value
    updateNodeProperty('data', nodeForm.value.data)
  }
}

const updateDataValue = (key: string, event: Event) => {
  const value = (event.target as HTMLInputElement).value
  nodeForm.value.data[key] = value
  updateNodeProperty('data', nodeForm.value.data)
}

// 监听选中节点变化
watch(() => props.selectedNode, (newNode) => {
  if (newNode) {
    nodeForm.value = {
      label: newNode.label || '',
      position: { ...newNode.position },
      size: { ...newNode.size },
      style: {
        fill: newNode.style?.fill || '#ffffff',
        stroke: newNode.style?.stroke || '#333333',
        strokeWidth: newNode.style?.strokeWidth || 2
      },
      data: { ...newNode.data }
    }
  }
}, { immediate: true })

// 监听选中边变化
watch(() => props.selectedEdge, (newEdge) => {
  if (newEdge) {
    edgeForm.value = {
      label: newEdge.label || '',
      style: {
        stroke: newEdge.style?.stroke || '#333333',
        strokeWidth: newEdge.style?.strokeWidth || 2
      }
    }
  }
}, { immediate: true })
</script>

<style scoped>
.flow-property-panel {
  font-size: 14px;
}

/* 滚动条样式 */
.flow-property-panel::-webkit-scrollbar {
  width: 6px;
}

.flow-property-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.flow-property-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.flow-property-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
