.bpmn-overlay {
  // font-size: 24px;
  cursor: pointer;
  // border-top: 50px solid transparent;
  // border-bottom: 50px solid transparent;
  // border-left: 100px solid #3adbf0; /* 黑色三角形，向右指 */
  // background-color: #3adbf0; /* 按钮颜色 */
  // clip-path: polygon(0 0, 100% 50%, 0 100%); /* 创建三角形 */
  // display: inline-block;
  transition: transform 0.3s ease; /* 平滑过渡效果 */
  &:hover {
    // box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    transform: scale(1.2);
  }
  &:active {
    transform: scale(0.9);
  }
}
.pause-node {
  color: #f00 !important;
}