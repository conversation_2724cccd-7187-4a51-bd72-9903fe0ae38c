import { defineStore } from "pinia";
import type Modeler from "bpmn-js/lib/Modeler";
import { toRaw } from "vue";
import { realXml2, realXml, xmlStr2 } from "../components/data/xmlStr";
import { debugApi } from "@/api";

// 联合类型
type ModelerRef = Modeler | undefined;
export type ModelerStore = {
  activeElement: BpmnElement | undefined;
  activeElementId: string | undefined;
  activeVariables: {
    variableName: string;
    variableValue: string;
    type: string;
    valueInfo: any;
  }[];
  debugModeler: ModelerRef;
  editModeler: ModelerRef;
  isDebugMode: boolean;
  isRunning: boolean;
  xmlStr: string;
  processDefId: string;
  dataId: string | undefined;
  overlayerManager: Record<string, any>;
  variablesManager: Record<string, any>;
  breakpoints: any[];
  currentBreakpoint: string | undefined;
};

const defaultState: ModelerStore = {
  activeElement: undefined,
  activeElementId: undefined,
  activeVariables: [],
  isDebugMode: false,
  isRunning: false,
  debugModeler: undefined,
  editModeler: undefined,
  processDefId: "Process_15y9jhq:2:135",
  dataId: undefined,
  xmlStr: realXml,
  overlayerManager: {},
  variablesManager: {},
  breakpoints: [],
  currentBreakpoint: undefined,
};

export default defineStore("modeler", {
  state: (): ModelerStore => defaultState,
  getters: {
    getActive: (state) => toRaw(state.activeElement),
    getActiveId: (state) => state.activeElementId,
    getIsDebugMode: (state) => state.isDebugMode,
    getDebugModeler: (state) => toRaw(state.debugModeler),
    getEditModeler: (state) => toRaw(state.editModeler),
    getDataId: (state) => state.dataId,
    getModeler: (state) =>
      state.isDebugMode ? toRaw(state.debugModeler) : toRaw(state.editModeler),
    getXmlStr: (state) => state.xmlStr,
    getProcessDefId: (state) => state.processDefId,
  },
  actions: {
    setModeler(debugModeler: ModelerRef, editModeler: ModelerRef) {
      this.debugModeler = debugModeler;
      this.editModeler = editModeler;
    },
    async changeMode(isDebugMode: boolean) {
      // 判断模式是否有变化
      if (this.isDebugMode === isDebugMode) {
        return;
      }
      const { xml, error } = await this.getModeler!.saveXML();
      if (xml) {
        this.xmlStr = xml;
        this.isDebugMode = isDebugMode;
      } else {
        throw error;
      }
      // 切换模式时，清空选中元素
      this.activeElement = undefined;
      this.activeElementId = undefined;
      if (this.isDebugMode) {
        this.cancelDebug();
      }
      await this.loadDiagram();
    },
    setActiveElement(elementId: string) {
      // if (!this.overlayerManager[elementId]?.variables) return;
      this.activeVariables = this.variablesManager[elementId];
      this.activeElementId = elementId;
    },
    async loadDiagram() {
      await this.getModeler!.importXML(this.xmlStr);
    },
    setHightlight(elementId: string) {
      const elementRegistry: any = this.getModeler!.get("elementRegistry");
      const element = elementRegistry.find((e: any) => e.id === elementId);
      const modeling: any = this.getModeler!.get("modeling");
      // 将高亮节点设置为虚线

      modeling.setColor(element as any, {
        stroke: "rgb(255, 0, 0)",
        strokeWidth: 3,
        strokeDasharray: "10,10",
      });
    },

    clearHightlight(elementId: string) {
      const elementRegistry: any = this.getModeler!.get("elementRegistry");
      const element = elementRegistry.find((e: any) => e.id === elementId);
      const modeling: any = this.getModeler!.get("modeling");
      // this.forceRemoveOverlay(element)
      modeling.setColor(element as any, {
        fill: "",
      });
    },

    forceRemoveOverlay(elementId: string) {
      if (!this.overlayerManager[elementId]) return;
      const overlays: any = this.getModeler!.get("overlays");
      const overlayElenment: any = this.overlayerManager[elementId];
      overlays.remove(overlayElenment.overlayId);
      // modelerStore.overlayer
    },
    cancelDebug() {
      this.isRunning = false;
      this.clearAllBreakpoints();
      this.activeElementId && this.clearHightlight(this.activeElementId);
    },
    clearAllBreakpoints() {
      this.breakpoints.forEach((breakpoint) => {
        this.forceRemoveOverlay(breakpoint.elementId);
      });
    },
    async setDefault() {
      // 所有状态恢复默认
      this.activeElementId && this.clearHightlight(this.activeElementId);
      this.clearAllBreakpoints();
      await debugApi.endDebug();
    },
  },
});
