<template>
  <div class="flow-control-panel h-full flex flex-col bg-gray-50">
    <!-- 标题栏 -->
    <div
      class="flex items-center justify-between p-4 border-b border-gray-200 bg-white"
    >
      <h3 class="text-lg font-semibold text-gray-800">节点面板</h3>
      <button @click="toggleCollapsed" class="p-1 rounded hover:bg-gray-100">
        <i :class="collapsed ? 'pi pi-angle-right' : 'pi pi-angle-left'" />
      </button>
    </div>

    <!-- 搜索栏 -->
    <div v-if="!collapsed" class="p-4 border-b border-gray-200 bg-white">
      <div class="relative">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索节点类型..."
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <i
          class="pi pi-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
        />
      </div>
    </div>

    <!-- 节点分类 -->
    <div v-if="!collapsed" class="flex-1 overflow-y-auto">
      <div
        v-for="category in filteredCategories"
        :key="category.name"
        class="border-b border-gray-200"
      >
        <!-- 分类标题 -->
        <button
          @click="toggleCategory(category.name)"
          class="w-full flex items-center justify-between p-4 text-left hover:bg-gray-100 transition-colors"
        >
          <div class="flex items-center space-x-2">
            <i :class="getCategoryIcon(category.name)" class="text-gray-600" />
            <span class="font-medium text-gray-800">{{
              getCategoryLabel(category.name)
            }}</span>
            <span class="text-sm text-gray-500"
              >({{ category.nodes.length }})</span
            >
          </div>
          <i
            :class="
              expandedCategories.has(category.name)
                ? 'pi pi-chevron-up'
                : 'pi pi-chevron-down'
            "
            class="text-gray-400"
          />
        </button>

        <!-- 节点列表 -->
        <div v-if="expandedCategories.has(category.name)" class="bg-gray-50">
          <div
            v-for="nodeType in category.nodes"
            :key="nodeType.id"
            :draggable="true"
            @dragstart="handleDragStart(nodeType, $event)"
            class="flex items-center p-3 mx-2 mb-2 bg-white rounded-lg border border-gray-200 cursor-move hover:shadow-md transition-all duration-200 hover:border-blue-300"
          >
            <!-- 节点图标 -->
            <div
              class="flex items-center justify-center w-10 h-10 rounded-lg mr-3"
              :style="{
                backgroundColor: nodeType.color + '20',
                color: nodeType.color,
              }"
            >
              <i :class="nodeType.icon || 'pi pi-circle'" class="text-lg" />
            </div>

            <!-- 节点信息 -->
            <div class="flex-1 min-w-0">
              <div class="font-medium text-gray-800 truncate">
                {{ nodeType.name }}
              </div>
              <div class="text-sm text-gray-500 truncate">
                {{ nodeType.description }}
              </div>
            </div>

            <!-- 拖拽提示 -->
            <div class="text-gray-400">
              <i class="pi pi-arrows-alt text-sm" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格拖拽区域 -->
    <div v-if="!collapsed" class="border-t border-gray-200 bg-white">
      <div class="p-4">
        <h4 class="font-medium text-gray-800 mb-3">表格数据</h4>
        <div
          ref="tableDropZone"
          @drop="handleTableDrop"
          @dragover.prevent
          @dragenter.prevent
          class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"
        >
          <i class="pi pi-table text-2xl text-gray-400 mb-2" />
          <p class="text-sm text-gray-600">拖拽表格行到此处</p>
          <p class="text-xs text-gray-500 mt-1">或点击选择数据源</p>
        </div>
      </div>
    </div>

    <!-- 折叠状态 -->
    <div
      v-if="collapsed"
      class="flex-1 flex flex-col items-center justify-center p-2"
    >
      <div v-for="category in categories" :key="category" class="mb-4">
        <button
          @click="
            expandedCategories.add(category);
            collapsed = false;
          "
          class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          :title="getCategoryLabel(category)"
        >
          <i :class="getCategoryIcon(category)" class="text-xl text-gray-600" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import type { FlowNodeType, DragData } from "../types";
import { nodeTypeRegistry } from "../nodes/simpleNodeTypes";

// Props
interface Props {
  nodeTypes?: FlowNodeType[];
}

const props = withDefaults(defineProps<Props>(), {
  nodeTypes: () => [],
});

// Emits
const emit = defineEmits<{
  "drag-start": [data: DragData, event: DragEvent];
  "table-data-selected": [data: any[]];
}>();

// 响应式数据
const collapsed = ref(false);
const searchQuery = ref("");
const expandedCategories = ref(new Set(["control", "process"]));
const tableDropZone = ref<HTMLElement>();

// 获取所有节点类型
const allNodeTypes = computed(() => {
  return props.nodeTypes.length > 0
    ? props.nodeTypes
    : nodeTypeRegistry.getAll();
});

// 获取所有分类
const categories = computed(() => {
  const cats = new Set<string>();
  allNodeTypes.value.forEach((type) => cats.add(type.category));
  return Array.from(cats);
});

// 过滤后的分类和节点
const filteredCategories = computed(() => {
  const query = searchQuery.value.toLowerCase();

  return categories.value
    .map((category) => {
      const nodes = allNodeTypes.value.filter((type) => {
        const matchesCategory = type.category === category;
        const matchesSearch =
          !query ||
          type.name.toLowerCase().includes(query) ||
          type.description?.toLowerCase().includes(query) ||
          type.category.toLowerCase().includes(query);

        return matchesCategory && matchesSearch;
      });

      return {
        name: category,
        nodes,
      };
    })
    .filter((category) => category.nodes.length > 0);
});

// 方法
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};

const toggleCategory = (category: string) => {
  if (expandedCategories.value.has(category)) {
    expandedCategories.value.delete(category);
  } else {
    expandedCategories.value.add(category);
  }
};

const getCategoryLabel = (category: string): string => {
  const labels: Record<string, string> = {
    control: "控制节点",
    process: "处理节点",
    event: "事件节点",
    data: "数据节点",
    integration: "集成节点",
  };
  return labels[category] || category;
};

const getCategoryIcon = (category: string): string => {
  const icons: Record<string, string> = {
    control: "pi pi-sitemap",
    process: "pi pi-cog",
    event: "pi pi-bell",
    data: "pi pi-database",
    integration: "pi pi-link",
  };
  return icons[category] || "pi pi-circle";
};

const handleDragStart = (nodeType: FlowNodeType, event: DragEvent) => {
  const dragData: DragData = {
    type: "node",
    nodeType: nodeType.id,
    data: {
      ...nodeType.defaultProps,
    },
  };

  emit("drag-start", dragData, event);
};

const handleTableDrop = (event: DragEvent) => {
  event.preventDefault();

  // 这里处理从外部拖拽进来的表格数据
  const data = event.dataTransfer?.getData("text/plain");
  if (data) {
    try {
      const tableData = JSON.parse(data);
      emit("table-data-selected", tableData);
    } catch (error) {
      console.error("Failed to parse table data:", error);
    }
  }
};

// 生命周期
onMounted(() => {
  // 默认展开前两个分类
  if (categories.value.length > 0) {
    expandedCategories.value.add(categories.value[0]);
    if (categories.value.length > 1) {
      expandedCategories.value.add(categories.value[1]);
    }
  }
});
</script>

<style scoped>
.flow-control-panel {
  user-select: none;
}

.flow-control-panel [draggable="true"] {
  cursor: grab;
}

.flow-control-panel [draggable="true"]:active {
  cursor: grabbing;
}

/* 拖拽时的样式 */
.flow-control-panel [draggable="true"]:hover {
  transform: translateY(-1px);
}

/* 滚动条样式 */
.flow-control-panel::-webkit-scrollbar {
  width: 6px;
}

.flow-control-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.flow-control-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.flow-control-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
