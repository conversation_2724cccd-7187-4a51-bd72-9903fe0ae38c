import { ModuleDeclaration } from 'didi'
import CustomContextPadProvider from './custom-context-pad'

// 在原本的contextpad模块上进行拓展
// const CustomContextPad: ModuleDeclaration = {
//   __init__: ['CustomContextPadProvider'],
//   CustomContextPadProvider: ['type', CustomContextPadProvider]
// }


// 直接覆盖原本的contextpad模块 关键在于contextPadProvider这个关键字的覆盖
const CustomContextPad: ModuleDeclaration = {
  __init__: ['contextPadProvider'],
  contextPadProvider: ['type', CustomContextPadProvider]
}

export default CustomContextPad
