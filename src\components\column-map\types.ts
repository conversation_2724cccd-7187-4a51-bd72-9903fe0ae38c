export type LineData = {
  table: string;
  field: string;
};

export type SourceTargetMap = {
  source: LineData;
  target: LineData;
};

export type FieldData = {
  name: string;
  type: string;
};

export type TableDirctionMap = {
  from: string;
  to: string;
}[];

export type EmitData = {
  lines: SourceTargetMap[];
  directions: TableDirctionMap;
};

export type LinePosition = {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
};

export type showLineData = {
  position: LinePosition;
  data: SourceTargetMap;
}[];
