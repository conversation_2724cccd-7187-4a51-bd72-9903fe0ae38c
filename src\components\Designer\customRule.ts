import RuleProvider from "diagram-js/lib/features/rules/RuleProvider";

// 第一步，继承 RuleProvider 创建自己的规则构造函数，并执行 init
// 这里需要注意的是，我们需要多依赖一个模块 rules, 用来保留原有的规则配置
class CustomRules extends RuleProvider {
  _rules: any;
  constructor(eventBus: any, rules: any) {
    super(eventBus);
    this.init();
  }

  init() {}
}

// 这里增加对 rules 模块的依赖
CustomRules.$inject = ["eventBus", "rules"];

// export default CustomRules

export default {
  __init__: ["customRules"],
  customRules: ["type", CustomRules],
};
