// 样式引入
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
@import 'bpmn-js/dist/assets/diagram-js.css'; // 左边工具栏以及编辑节点的样式
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
// @import 'bpmn-js-properties-panel/dist/assets/properties-panel.css';
@import '@/bpmn-js-token-simulation-lib/assets/css/bpmn-js-token-simulation.css';

.break-point {
  > rect {
    stroke: red;
    stroke-width: 3px;
  }
}

// .djs-palette {
//   display: none;
// }

// .djs-outline {
//   display: none;
// }

.enhancement-op {
  background: url('./1.png');
  background-size: 100% 100%;
  &:hover {
    background: url('./2.png');
  }
}