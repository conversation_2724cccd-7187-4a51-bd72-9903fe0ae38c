export enum NodeTypes {
  "rabbitMQ" = "rabbitMQ",
  "http" = "http",
}

export const config: Partial<Record<NodeTypes, any>> = {
  rabbitMQ: {
    type: NodeTypes.rabbitMQ,
    bpmnType: "ScriptTask",
    // bpmnType: "TerminateEventDefinition",
    style: {
      width: 200,
      height: 50,
      color: "#FFC107",
      textContent: "Script",
      icon: "1.png",
    },
  },
  http: {
    type: NodeTypes.http,
    bpmnType: "EndEvent",
    style: {
      width: 200,
      height: 50,
      color: "#4CAF50",
      textContent: "HTTP触发",
      icon: "2.png",
    },
  },
};

export function isCustomNode(type: any) {
  return Object.values(NodeTypes).includes(type);
}

export default config;
