<template>
  <div class="minimal-test">
    <h2>最简单的拖拽测试</h2>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <p>Hook 状态: {{ hookLoaded ? "已加载" : "未加载" }}</p>
      <p>canDrag 函数: {{ typeof canDrag }}</p>
      <p>getRowClasses 函数: {{ typeof getRowClasses }}</p>
      <p>拖拽状态: {{ dragState.isDragging ? "拖拽中" : "未拖拽" }}</p>
    </div>

    <div class="test-data">
      <h3>测试数据：</h3>
      <table class="simple-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>名称</th>
            <th>状态</th>
            <th>可拖拽</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in testData"
            :key="item.id"
            :class="getRowClasses(item, index)"
            :draggable="canDrag(item)"
            @dragstart="onDragStart($event, index)"
            @dragover="onDragOver($event)"
            @drop="onDrop($event, index)"
            @dragend="onDragEnd"
            style="border: 1px solid #ccc; margin: 2px 0"
          >
            <td>{{ item.id }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.disabled ? "禁用" : "启用" }}</td>
            <td>{{ canDrag(item) ? "是" : "否" }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="test-buttons">
      <button @click="testBasicFunction">测试基本函数</button>
      <button @click="resetData">重置数据</button>
    </div>

    <div class="test-results">
      <h3>测试结果：</h3>
      <pre>{{ testResults }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useDragReorder } from "./hooks/useDragReorder";

// 测试数据
const testData = ref([
  { id: 1, name: "项目1", disabled: false },
  { id: 2, name: "项目2", disabled: false },
  { id: 3, name: "项目3", disabled: true },
  { id: 4, name: "项目4", disabled: false },
]);

// 测试结果
const testResults = ref("");
const hookLoaded = ref(false);

// 使用 Hook
let dragState: any,
  canDrag: any,
  getRowClasses: any,
  handleDragStart: any,
  handleDragOver: any,
  handleDrop: any,
  handleDragEnd: any;

try {
  const hookResult = useDragReorder({
    keyField: "id",
    disabledField: "disabled",
    displayField: "name",
    debug: true,
  });

  dragState = hookResult.dragState;
  canDrag = hookResult.canDrag;
  getRowClasses = hookResult.getRowClasses;
  handleDragStart = hookResult.handleDragStart;
  handleDragOver = hookResult.handleDragOver;
  handleDrop = hookResult.handleDrop;
  handleDragEnd = hookResult.handleDragEnd;

  hookLoaded.value = true;
  testResults.value = "Hook 加载成功!";
} catch (error) {
  testResults.value = `Hook 加载失败: ${error}`;
  console.error("Hook 加载错误:", error);

  // 提供默认函数
  dragState = ref({ isDragging: false });
  canDrag = () => false;
  getRowClasses = () => [];
  handleDragStart = () => {};
  handleDragOver = () => {};
  handleDrop = () => {};
  handleDragEnd = () => {};
}

// 事件处理
const onDragStart = (event: DragEvent, index: number) => {
  console.log("开始拖拽:", index);
  testResults.value += `\n开始拖拽项目 ${index}`;
  if (handleDragStart) {
    handleDragStart(event, index, testData.value);
  }
};

const onDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (handleDragOver) {
    handleDragOver(event);
  }
};

const onDrop = (event: DragEvent, index: number) => {
  console.log("放下到:", index);
  testResults.value += `\n放下到项目 ${index}`;

  if (handleDrop) {
    const result = handleDrop(event, index, testData.value);
    if (result) {
      testData.value = result.newData;
      testResults.value += `\n重排序成功: ${result.newData
        .map((item) => item.name)
        .join(", ")}`;
    }
  }
};

const onDragEnd = () => {
  console.log("拖拽结束");
  testResults.value += "\n拖拽结束";
  if (handleDragEnd) {
    handleDragEnd();
  }
};

// 测试函数
const testBasicFunction = () => {
  testResults.value = "=== 基本函数测试 ===\n";

  testData.value.forEach((item, index) => {
    const canDragResult = canDrag ? canDrag(item) : "N/A";
    const classesResult = getRowClasses ? getRowClasses(item, index) : "N/A";

    testResults.value += `项目${
      item.id
    }: 可拖拽=${canDragResult}, 样式=${JSON.stringify(classesResult)}\n`;
  });
};

const resetData = () => {
  testData.value = [
    { id: 1, name: "项目1", disabled: false },
    { id: 2, name: "项目2", disabled: false },
    { id: 3, name: "项目3", disabled: true },
    { id: 4, name: "项目4", disabled: false },
  ];
  testResults.value = "数据已重置";
};
</script>

<style scoped>
.minimal-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  color: #333;
  background: white;
}

.debug-info,
.test-data,
.test-buttons,
.test-results {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #f9f9f9;
  color: #333;
}

.simple-table {
  width: 100%;
  border-collapse: collapse;
}

.simple-table th,
.simple-table td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #ddd;
  color: #333;
}

.simple-table th {
  background: #f0f0f0;
  font-weight: bold;
  color: #333;
}

.simple-table tr:hover {
  background: #f5f5f5;
}

.simple-table tr[draggable="true"] {
  cursor: move;
}

.simple-table tr[draggable="false"] {
  opacity: 0.6;
  cursor: not-allowed;
}

button {
  margin: 5px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}

pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
  color: #333;
}

.drag-table-row {
  transition: background-color 0.2s;
}

.disabled-row {
  background-color: #f8f9fa;
  color: #6c757d;
}

.dragging-source {
  opacity: 0.5;
  background-color: #e3f2fd;
}

.drag-hover {
  background-color: #fff3cd;
}
</style>
