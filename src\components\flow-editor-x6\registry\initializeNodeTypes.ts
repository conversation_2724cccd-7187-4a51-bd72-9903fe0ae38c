import { nodeRegistry } from "./NodeRegistry";
import { StartNode, EndNode, DecisionNode } from "../nodes/ControlNodes";
import { ProcessNode, ScriptNode } from "../nodes/ProcessNodes";
import { DatabaseNode } from "../nodes/DataNodes";

/**
 * 初始化所有节点类型
 */
export function initializeNodeTypes(): void {
  // 控制节点
  try {
    nodeRegistry.registerNodeType(new StartNode().toFlowNodeType());
    nodeRegistry.registerNodeType(new EndNode().toFlowNodeType());
    nodeRegistry.registerNodeType(new DecisionNode().toFlowNodeType());
  } catch (error) {
    console.warn("ControlNodes not available:", error);
  }

  // 流程节点
  try {
    nodeRegistry.registerNodeType(new ProcessNode().toFlowNodeType());
    nodeRegistry.registerNodeType(new ScriptNode().toFlowNodeType());
  } catch (error) {
    console.warn("ProcessNodes not available:", error);
  }

  // 数据节点
  try {
    nodeRegistry.registerNodeType(new DatabaseNode().toFlowNodeType());
  } catch (error) {
    console.warn("DataNodes not available:", error);
  }

  // 注册基础节点类型（如果上面的类不存在）
  const basicNodeTypes = [
    {
      id: "process",
      name: "处理",
      category: "process",
      icon: "pi pi-cog",
      color: "#2196F3",
      description: "基础处理节点",
      defaultSize: { width: 120, height: 60 },
      defaultProps: {},
      validationRules: [],
      allowedConnections: {
        input: ["*"],
        output: ["*"],
      },
    },
    {
      id: "decision",
      name: "决策",
      category: "control",
      icon: "pi pi-question",
      color: "#FF9800",
      description: "条件判断节点",
      defaultSize: { width: 100, height: 80 },
      defaultProps: {
        condition: "",
      },
      validationRules: [],
      allowedConnections: {
        input: ["*"],
        output: ["*"],
      },
    },
    {
      id: "data",
      name: "数据",
      category: "data",
      icon: "pi pi-database",
      color: "#9C27B0",
      description: "数据处理节点",
      defaultSize: { width: 120, height: 60 },
      defaultProps: {},
      validationRules: [],
      allowedConnections: {
        input: ["*"],
        output: ["*"],
      },
    },
    {
      id: "timer",
      name: "定时器",
      category: "event",
      icon: "pi pi-clock",
      color: "#4CAF50",
      description: "定时触发节点",
      defaultSize: { width: 80, height: 80 },
      defaultProps: {
        interval: 1000,
      },
      validationRules: [],
      allowedConnections: {
        input: [],
        output: ["*"],
      },
    },
  ];

  // 注册基础节点类型
  basicNodeTypes.forEach((nodeType) => {
    if (!nodeRegistry.getNodeType(nodeType.id)) {
      nodeRegistry.registerNodeType(nodeType);
    }
  });
}
