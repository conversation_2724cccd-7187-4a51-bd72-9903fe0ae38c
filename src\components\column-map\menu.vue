<template>
  <ContextMenu :model="items" ref="contextMenu" style="z-index: 100;">
    <template #item="{ item }">
      <div class="p-menu-item" @click="handleLineEvent(item)" style="cursor: pointer;">
        {{ getIsChoosed(item) ? '✔ ' : '' }}
        {{ item.label }}
      </div>
    </template>
  </ContextMenu>
</template>

<script setup lang="ts">
import { ref, PropType, Ref } from 'vue';
import ColumnMap from './index.vue'
import ContextMenu from 'primevue/contextmenu';

enum MenuItem {
  REMOVE = 'REMOVE',
  LeftToRight = 'LeftToRight',
  RightToLeft = 'RightToLeft'
}

const props = defineProps({
  columnMap: {
    type: Object as PropType<InstanceType<typeof ColumnMap>>,
    required: true
  },
})

let currentLine: any = null;
const contextMenu = ref<InstanceType<typeof ContextMenu>>();

const items = [
  {
    label: 'remove',
    icon: 'pi pi-language',
    value: MenuItem.REMOVE
  },
  {
    label: 'left to right',
    icon: 'pi pi-volume-up',
    value: MenuItem.LeftToRight
  },
  {
    label: 'right to left',
    icon: 'pi pi-print',
    value: MenuItem.RightToLeft
  }
];

function getIsChoosed(item: any) {
  if (item.value === MenuItem.REMOVE || !currentLine) return false;
  const { tableDirctionMap } = props.columnMap.getLineData();
  const { source, target } = currentLine.data;

  if (item.value === MenuItem.LeftToRight) {
    return tableDirctionMap.some((item: any) => item.from === source.table && item.to === target.table);
  }
  if (item.value === MenuItem.RightToLeft) {
    return tableDirctionMap.some((item: any) => item.from === target.table && item.to === source.table);
  }
  return false;
}

function handleLineEvent(item: any) {
  const lineData = currentLine.data;
  if (item.value === MenuItem.REMOVE) {
    props.columnMap?.removeLine(lineData);
  }
  if (item.value === MenuItem.LeftToRight) {
    if (getIsChoosed(item)) {
      props.columnMap.removeTableDirction(lineData.source.table, lineData.target.table);
    } else {
      props.columnMap.addTableDirction(lineData.source.table, lineData.target.table);
    }
  }
  if (item.value === MenuItem.RightToLeft) {
    if (getIsChoosed(item)) {
      props.columnMap.removeTableDirction(lineData.target.table, lineData.source.table);
    } else {
      props.columnMap.addTableDirction(lineData.target.table, lineData.source.table);
    }
  }
}

function showMenu(event: MouseEvent, line: any) {
  currentLine = line;
  console.log('%c [ currentLine ]-88', 'font-size:13px; background:pink; color:#bf2c9f;', currentLine)
  contextMenu.value!.show(event)
}

defineExpose({
  showMenu,
})

</script>

<style scoped></style>