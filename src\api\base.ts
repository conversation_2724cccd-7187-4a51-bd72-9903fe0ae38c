import axios from "axios";

const baseURL = "/api/ama";
// const baseURL = 'http://localhost' + "/api/ama";

const instance = axios.create({
  baseURL,
  headers: {},
});

async function require(url: string, params?: any) {
  const res = await instance.post(url, params);
  if (res.status !== 200) {
    throw new Error("请求失败");
  }
  if (res.data.data !== undefined || res.data.data !== null)
    return res.data.data;
  console.error(res);
  throw new Error("请求失败");
}

// 改变sessionId
export function changeSessionId(sessionId: string) {
  instance.defaults.headers.SESSION_ID = sessionId;
}

export default require;
