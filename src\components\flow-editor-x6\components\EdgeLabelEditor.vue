<template>
  <div
    v-if="visible"
    class="edge-label-editor absolute z-50 bg-white border border-gray-300 rounded shadow-lg"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
  >
    <div class="p-2">
      <InputText
        ref="inputRef"
        v-model="labelText"
        placeholder="输入连线标签..."
        class="w-32"
        size="small"
        @keydown.enter="handleSave"
        @keydown.escape="handleCancel"
        @blur="handleSave"
      />
      <div class="flex gap-1 mt-2">
        <Button
          icon="pi pi-check"
          size="small"
          severity="success"
          @click="handleSave"
        />
        <Button
          icon="pi pi-times"
          size="small"
          severity="secondary"
          @click="handleCancel"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'

// Props
interface Props {
  visible: boolean
  position: { x: number; y: number }
  initialValue?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialValue: ''
})

// Emits
const emit = defineEmits<{
  save: [value: string]
  cancel: []
}>()

// Refs
const inputRef = ref<HTMLInputElement>()
const labelText = ref('')

// Watch for visibility changes to focus input
watch(() => props.visible, async (visible) => {
  if (visible) {
    labelText.value = props.initialValue
    await nextTick()
    inputRef.value?.focus()
    inputRef.value?.select()
  }
})

// Methods
const handleSave = () => {
  emit('save', labelText.value.trim())
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.edge-label-editor {
  min-width: 150px;
}
</style>
