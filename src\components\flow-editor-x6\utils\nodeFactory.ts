import type { FlowNode, FlowNodeType, FlowPosition, FlowSize } from '../types'
import { generateId } from './common'

/**
 * 默认节点大小
 */
export const DEFAULT_NODE_SIZE: FlowSize = {
  width: 120,
  height: 60
}

/**
 * 创建节点
 */
export function createNode(
  type: string,
  position: FlowPosition,
  options: {
    label?: string
    size?: FlowSize
    data?: Record<string, any>
    style?: Record<string, any>
  } = {}
): FlowNode {
  const {
    label = type,
    size = DEFAULT_NODE_SIZE,
    data = {},
    style = {}
  } = options

  return {
    id: generateId('node'),
    type,
    label,
    position: { ...position },
    size: { ...size },
    data: { ...data },
    style: { ...style },
    ports: [],
    validationState: 'valid',
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }
}

/**
 * 从节点类型创建节点
 */
export function createNodeFromType(
  nodeType: FlowNodeType,
  position: FlowPosition,
  customData: Record<string, any> = {}
): FlowNode {
  const size = nodeType.defaultSize || DEFAULT_NODE_SIZE
  const data = { ...nodeType.defaultProps, ...customData }
  
  const style = {
    fill: nodeType.color || '#ffffff',
    stroke: '#333333',
    strokeWidth: 1,
    ...nodeType.defaultProps?.style
  }

  return createNode(nodeType.id, position, {
    label: nodeType.name,
    size,
    data,
    style
  })
}

/**
 * 克隆节点
 */
export function cloneNode(node: FlowNode, newPosition?: FlowPosition): FlowNode {
  const cloned: FlowNode = {
    ...node,
    id: generateId('node'),
    position: newPosition || { x: node.position.x + 20, y: node.position.y + 20 },
    data: { ...node.data },
    style: { ...node.style },
    ports: node.ports?.map(port => ({ ...port })) || [],
    metadata: {
      ...node.metadata,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      clonedFrom: node.id
    }
  }

  return cloned
}

/**
 * 批量创建节点
 */
export function createNodes(
  configs: Array<{
    type: string
    position: FlowPosition
    options?: Parameters<typeof createNode>[2]
  }>
): FlowNode[] {
  return configs.map(config => createNode(config.type, config.position, config.options))
}

/**
 * 验证节点数据
 */
export function validateNodeData(node: FlowNode): string[] {
  const errors: string[] = []

  if (!node.id) {
    errors.push('Node ID is required')
  }

  if (!node.type) {
    errors.push('Node type is required')
  }

  if (!node.label) {
    errors.push('Node label is required')
  }

  if (node.size.width <= 0) {
    errors.push('Node width must be greater than 0')
  }

  if (node.size.height <= 0) {
    errors.push('Node height must be greater than 0')
  }

  return errors
}

/**
 * 获取节点边界框
 */
export function getNodeBounds(node: FlowNode) {
  return {
    x: node.position.x,
    y: node.position.y,
    width: node.size.width,
    height: node.size.height,
    right: node.position.x + node.size.width,
    bottom: node.position.y + node.size.height,
    centerX: node.position.x + node.size.width / 2,
    centerY: node.position.y + node.size.height / 2
  }
}

/**
 * 检查节点是否重叠
 */
export function isNodeOverlapping(node1: FlowNode, node2: FlowNode): boolean {
  const bounds1 = getNodeBounds(node1)
  const bounds2 = getNodeBounds(node2)

  return !(
    bounds1.right < bounds2.x ||
    bounds2.right < bounds1.x ||
    bounds1.bottom < bounds2.y ||
    bounds2.bottom < bounds1.y
  )
}

/**
 * 自动布局节点
 */
export function autoLayoutNodes(nodes: FlowNode[], options: {
  direction?: 'horizontal' | 'vertical'
  spacing?: number
  startPosition?: FlowPosition
} = {}): FlowNode[] {
  const {
    direction = 'horizontal',
    spacing = 50,
    startPosition = { x: 100, y: 100 }
  } = options

  let currentX = startPosition.x
  let currentY = startPosition.y

  return nodes.map(node => {
    const newNode = {
      ...node,
      position: { x: currentX, y: currentY }
    }

    if (direction === 'horizontal') {
      currentX += node.size.width + spacing
    } else {
      currentY += node.size.height + spacing
    }

    return newNode
  })
}

/**
 * 对齐节点
 */
export function alignNodes(nodes: FlowNode[], alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'): FlowNode[] {
  if (nodes.length < 2) return nodes

  const bounds = nodes.map(getNodeBounds)
  
  let targetValue: number

  switch (alignment) {
    case 'left':
      targetValue = Math.min(...bounds.map(b => b.x))
      return nodes.map(node => ({ ...node, position: { ...node.position, x: targetValue } }))
    
    case 'right':
      targetValue = Math.max(...bounds.map(b => b.right))
      return nodes.map(node => ({ ...node, position: { ...node.position, x: targetValue - node.size.width } }))
    
    case 'center':
      targetValue = (Math.min(...bounds.map(b => b.x)) + Math.max(...bounds.map(b => b.right))) / 2
      return nodes.map(node => ({ ...node, position: { ...node.position, x: targetValue - node.size.width / 2 } }))
    
    case 'top':
      targetValue = Math.min(...bounds.map(b => b.y))
      return nodes.map(node => ({ ...node, position: { ...node.position, y: targetValue } }))
    
    case 'bottom':
      targetValue = Math.max(...bounds.map(b => b.bottom))
      return nodes.map(node => ({ ...node, position: { ...node.position, y: targetValue - node.size.height } }))
    
    case 'middle':
      targetValue = (Math.min(...bounds.map(b => b.y)) + Math.max(...bounds.map(b => b.bottom))) / 2
      return nodes.map(node => ({ ...node, position: { ...node.position, y: targetValue - node.size.height / 2 } }))
    
    default:
      return nodes
  }
}

/**
 * 分布节点
 */
export function distributeNodes(nodes: FlowNode[], direction: 'horizontal' | 'vertical'): FlowNode[] {
  if (nodes.length < 3) return nodes

  const bounds = nodes.map(getNodeBounds)
  const sortedNodes = [...nodes]

  if (direction === 'horizontal') {
    sortedNodes.sort((a, b) => a.position.x - b.position.x)
    const leftmost = Math.min(...bounds.map(b => b.x))
    const rightmost = Math.max(...bounds.map(b => b.right))
    const totalWidth = rightmost - leftmost
    const spacing = totalWidth / (nodes.length - 1)

    return sortedNodes.map((node, index) => ({
      ...node,
      position: { ...node.position, x: leftmost + spacing * index }
    }))
  } else {
    sortedNodes.sort((a, b) => a.position.y - b.position.y)
    const topmost = Math.min(...bounds.map(b => b.y))
    const bottommost = Math.max(...bounds.map(b => b.bottom))
    const totalHeight = bottommost - topmost
    const spacing = totalHeight / (nodes.length - 1)

    return sortedNodes.map((node, index) => ({
      ...node,
      position: { ...node.position, y: topmost + spacing * index }
    }))
  }
}
