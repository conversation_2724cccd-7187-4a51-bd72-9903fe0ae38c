<template>
  <div class="palette">
    <a-button type="primary" @click="useOperateTool('handTool')">抓手工具</a-button>
    <a-button type="primary" @click="useOperateTool('lassoTool')">拉索工具</a-button>
    <a-button type="primary" @click="useOperateTool('spaceTool')">平移工具</a-button>
    <a-button type="primary" @click="useOperateTool('globalConnect')">连线工具</a-button>
    <create-element v-for="nodeType in NodeTypes" :key="nodeType" :nodeType="nodeType" />
    <a-button type="primary" @click="saveXml">保存xml</a-button>
  </div>
</template>

<script lang="ts" setup>
// 实现自定义属性面板
import { ref, watch } from 'vue';
import useModelerStore from '@/store/modeler'
import { debugApi } from '@/api'
import { NodeTypes, config as renderConfig } from '@/renderer/config'
import CreateElement from './CreateElement.vue';

const modelerStore = useModelerStore()

async function saveXml() {
  const xml = await modelerStore.getModeler!.saveXML()
  console.log(xml)
}

function useOperateTool(toolName: string) {
  (modelerStore.getModeler!.get(toolName) as any).toggle()
}


async function nextStep() {
  await debugApi.step(modelerStore.getDataId as any)
}

async function stopDiagram() {
  await debugApi.endDebug()
}

watch(() => modelerStore.activeElementId, (newVal, oldVal) => {
  if (oldVal) {
    modelerStore.clearHightlight(oldVal as any)
  }
  if (newVal) {
    modelerStore.setHightlight(newVal as any)
  }
})

</script>

<style lang="scss" scoped>
.palette {
  height: 100%;
  width: 250px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  height: 100%;

  .create-element-button {
    margin-top: 20px;
    width: 200px;
  }
}
</style>