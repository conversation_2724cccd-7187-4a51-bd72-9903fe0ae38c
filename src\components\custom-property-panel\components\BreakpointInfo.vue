<template>
  <a-table :columns="columns" :data="modelerStore.activeVariables" pagination>
  </a-table>
</template>

<script setup lang="ts">
import useModelerStore from '@/store/modeler'

const modelerStore = useModelerStore()

const columns = [
  {
    title: 'Variable',
    dataIndex: 'variableName',
  },
  {
    title: 'Type',
    dataIndex: 'type',
  },
  {
    title: 'Value',
    dataIndex: 'variableValue',
  },
]

</script>

<style lang="scss">
.custom-property-panel {
  .arco-form {
    height: 200px
  }
}
</style>