<template>
  <div class="test-hook">
    <h2>Hook 测试</h2>

    <div class="test-section">
      <h3>测试数据：</h3>
      <div class="data-display">
        <div v-for="(item, index) in testData" :key="item.id" class="data-item">
          {{ index + 1 }}. {{ item.name }} ({{
            item.disabled ? "禁用" : "启用"
          }})
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>Hook 状态：</h3>
      <div class="status-display">
        <p>拖拽状态: {{ dragState.isDragging ? "拖拽中" : "未拖拽" }}</p>
        <p>源索引: {{ dragState.sourceIndex }}</p>
        <p>悬停索引: {{ dragState.hoverIndex }}</p>
        <p>插入位置: {{ dragState.insertPosition || "无" }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>测试按钮：</h3>
      <div class="button-group">
        <button @click="testReorder" class="test-btn">测试重排序 (1→3)</button>
        <button @click="testCanDrag" class="test-btn">测试拖拽权限</button>
        <button @click="testRowClasses" class="test-btn">测试行样式</button>
        <button @click="resetTest" class="test-btn">重置测试</button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试结果：</h3>
      <div class="result-display">
        <pre>{{ testResults }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useDragReorder } from "./hooks/useDragReorder";

// 测试数据
const testData = ref([
  { id: 1, name: "项目1", disabled: false },
  { id: 2, name: "项目2", disabled: false },
  { id: 3, name: "项目3", disabled: true },
  { id: 4, name: "项目4", disabled: false },
  { id: 5, name: "项目5", disabled: true },
]);

// 使用 Hook
const {
  dragState,
  canDrag,
  getRowClasses,
  handleDragStart,
  handleDragOver,
  handleDragEnter,
  handleDragLeave,
  handleDrop,
  handleDragEnd,
} = useDragReorder({
  keyField: "id",
  displayField: "name",
  disabledField: "disabled",
  debug: true,
});

// 测试结果
const testResults = ref("");

// 测试函数
const testReorder = () => {
  console.log("开始测试重排序...");
  console.log("测试数据:", testData.value);
  console.log("canDrag 函数:", canDrag);

  // 测试 canDrag 函数
  const canDragResults = testData.value.map((item, index) => {
    const result = canDrag.value(item);
    console.log(`项目 ${item.name} 可拖拽: ${result}`);
    return result;
  });

  // 模拟拖拽事件
  const mockEvent = new DragEvent("drop", {
    dataTransfer: new DataTransfer(),
  });

  console.log("开始拖拽事件...");

  // 测试从索引 0 拖拽到索引 2 (禁用项目)
  const startResult = handleDragStart(mockEvent, 0, testData.value);
  console.log("拖拽开始结果:", startResult);
  console.log("拖拽状态:", dragState.value);

  const result = handleDrop(mockEvent, 2, testData.value);
  console.log("拖拽结果:", result);

  if (result) {
    testData.value = result.newData;
    testResults.value = `重排序成功!\n新顺序: ${result.newData
      .map((item) => item.name)
      .join(", ")}\n\ncanDrag 测试: ${canDragResults.join(", ")}`;
  } else {
    testResults.value = `重排序失败!\ncanDrag 测试: ${canDragResults.join(
      ", "
    )}\n拖拽状态: ${JSON.stringify(dragState.value, null, 2)}`;
  }

  handleDragEnd();
};

const testCanDrag = () => {
  const results = testData.value.map((item, index) => {
    const canDragResult = canDrag.value(item);
    return `${item.name}: ${canDragResult ? "可拖拽" : "不可拖拽"}`;
  });

  testResults.value = `拖拽权限测试:\n${results.join("\n")}`;
};

const testRowClasses = () => {
  // 模拟拖拽状态
  dragState.value = {
    isDragging: true,
    sourceIndex: 0,
    hoverIndex: 1,
    draggedItem: testData.value[0],
    insertPosition: "after",
  };

  const results = testData.value.map((item, index) => {
    const classes = getRowClasses.value(item, index);
    return `${item.name}: [${classes.join(", ")}]`;
  });

  testResults.value = `行样式测试:\n${results.join("\n")}`;

  // 重置拖拽状态
  setTimeout(() => {
    handleDragEnd();
  }, 2000);
};

const resetTest = () => {
  testData.value = [
    { id: 1, name: "项目1", disabled: false },
    { id: 2, name: "项目2", disabled: false },
    { id: 3, name: "项目3", disabled: true },
    { id: 4, name: "项目4", disabled: false },
    { id: 5, name: "项目5", disabled: true },
  ];
  testResults.value = "";
  handleDragEnd();
};
</script>

<style scoped>
.test-hook {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.test-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.data-display {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.data-item {
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.status-display p {
  margin: 5px 0;
  font-family: monospace;
  background: white;
  padding: 5px 10px;
  border-radius: 4px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background: #0056b3;
}

.result-display {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.result-display pre {
  margin: 0;
  font-family: monospace;
  font-size: 14px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
