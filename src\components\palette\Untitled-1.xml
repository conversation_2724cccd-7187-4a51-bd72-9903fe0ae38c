"<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:custom="http://custom-bpmn.io/schema/bpmn" id="_UMTjANlIEeO126z8O3IuJg"
  targetNamespace="http://activiti.org/bpmn" exporter="camunda modeler" exporterVersion="2.5.0"
  xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
  <bpmn2:process id="Process_1" isExecutable="true">
    <bpmn2:startEvent id="StartEvent_1">
      <bpmn2:outgoing>SequenceFlow_1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:sequenceFlow id="SequenceFlow_1" name="" sourceRef="StartEvent_1" targetRef="loadOrder" />
    <bpmn2:scriptTask id="loadOrder" name="Load order" scriptFormat="Javascript">
      <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
      <bpmn2:script>print("LOAD ORDER");execution.setVariable("data", "ORDER_DATA");</bpmn2:script>
    </bpmn2:scriptTask>
    <bpmn2:scriptTask id="calcSum" name="Calculate sum" scriptFormat="Javascript">
      <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
      <bpmn2:script>execution.setVariable("sum", 100);</bpmn2:script>
    </bpmn2:scriptTask>
    <bpmn2:sequenceFlow id="SequenceFlow_2" name="" sourceRef="loadOrder" targetRef="calcSum" />
    <bpmn2:sequenceFlow id="SequenceFlow_8" name="" sourceRef="calcSum" targetRef="ScriptTask_3" />
    <bpmn2:scriptTask id="ScriptTask_3" name="Handle order" scriptFormat="Javascript">
      <bpmn2:outgoing>SequenceFlow_5</bpmn2:outgoing>
      <bpmn2:script>print("ORDER HANDLED " + execution.getVariable("data"));</bpmn2:script>
    </bpmn2:scriptTask>
    <bpmn2:sequenceFlow id="SequenceFlow_5" name="" sourceRef="ScriptTask_3" targetRef="EndEvent_1" />
    <bpmn2:endEvent id="EndEvent_1">
      <bpmn2:incoming>SequenceFlow_5</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:scriptTask id="Activity_1xrnbc6" custom:nodeType="rabbitMQ" value="123456">
      <bpmn2:script>test-scirpt</bpmn2:script>
    </bpmn2:scriptTask>
  </bpmn2:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_6" bpmnElement="StartEvent_1">
        <dc:Bounds x="150" y="216" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="168" y="257" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_ScriptTask_4" bpmnElement="loadOrder">
        <dc:Bounds x="276" y="194" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_ScriptTask_5" bpmnElement="calcSum">
        <dc:Bounds x="426" y="194" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_ScriptTask_6" bpmnElement="ScriptTask_3">
        <dc:Bounds x="576" y="194" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_EndEvent_7" bpmnElement="EndEvent_1">
        <dc:Bounds x="773" y="216" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="791" y="257" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xrnbc6_di" bpmnElement="Activity_1xrnbc6">
        <dc:Bounds x="120" y="385" width="200" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_SequenceFlow_1" bpmnElement="SequenceFlow_1"
        sourceElement="_BPMNShape_StartEvent_6" targetElement="_BPMNShape_ScriptTask_4">
        <di:waypoint x="186" y="234" />
        <di:waypoint x="276" y="234" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="208" y="234" width="6" height="6" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_SequenceFlow_2" bpmnElement="SequenceFlow_2"
        sourceElement="_BPMNShape_ScriptTask_4" targetElement="_BPMNShape_ScriptTask_5">
        <di:waypoint x="376" y="234" />
        <di:waypoint x="426" y="234" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_SequenceFlow_8" bpmnElement="SequenceFlow_8"
        sourceElement="_BPMNShape_ScriptTask_5" targetElement="_BPMNShape_ScriptTask_6">
        <di:waypoint x="526" y="234" />
        <di:waypoint x="576" y="234" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="572" y="234" width="6" height="6" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_SequenceFlow_5" bpmnElement="SequenceFlow_5"
        sourceElement="_BPMNShape_ScriptTask_6" targetElement="_BPMNShape_EndEvent_7">
        <di:waypoint x="676" y="234" />
        <di:waypoint x="773" y="234" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="698" y="234" width="6" height="6" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>"