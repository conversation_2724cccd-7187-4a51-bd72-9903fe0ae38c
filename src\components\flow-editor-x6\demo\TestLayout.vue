<template>
  <div class="test-layout">
    <!-- 顶部标题栏 -->
    <div class="header">
      <h1>流程编辑器测试</h1>
      <p>测试布局是否正常</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <h3>左侧面板</h3>
        <div class="panel-item">节点1</div>
        <div class="panel-item">节点2</div>
        <div class="panel-item">节点3</div>
      </div>

      <!-- 中间画布 -->
      <div class="center-canvas">
        <!-- 工具栏 -->
        <div class="toolbar">
          <button class="btn btn-blue">保存</button>
          <button class="btn btn-green">示例</button>
          <button class="btn btn-red">清空</button>
        </div>
        
        <!-- 画布区域 -->
        <div class="canvas-area">
          <div class="canvas-placeholder">
            <div class="icon">🎨</div>
            <div class="title">画布区域</div>
            <div class="subtitle">拖拽节点到这里</div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <h3>右侧面板</h3>
        <p>属性编辑区域</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 简单的测试组件，不需要任何逻辑
</script>

<style scoped>
.test-layout {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f3f4f6;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 1.5rem;
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
}

.header p {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 320px;
  background-color: #f9fafb;
  border-right: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.left-panel h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.panel-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.panel-item:hover {
  background-color: #f3f4f6;
  border-color: #3b82f6;
}

.center-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.toolbar {
  height: 3rem;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-blue {
  background-color: #3b82f6;
  color: white;
}

.btn-blue:hover {
  background-color: #2563eb;
}

.btn-green {
  background-color: #10b981;
  color: white;
}

.btn-green:hover {
  background-color: #059669;
}

.btn-red {
  background-color: #ef4444;
  color: white;
}

.btn-red:hover {
  background-color: #dc2626;
}

.canvas-area {
  flex: 1;
  background-color: #f9fafb;
  position: relative;
  background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

.canvas-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #6b7280;
}

.icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 0.875rem;
}

.right-panel {
  width: 320px;
  background-color: #f9fafb;
  border-left: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.right-panel h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.right-panel p {
  margin: 0;
  color: #6b7280;
  text-align: center;
}
</style>
