import type { ValidationRule, ValidationContext, ValidationResult } from '../types'

/**
 * 验证规则管理器
 */
export class ValidationRuleManager {
  private rules = new Map<string, ValidationRule>()
  private ruleCategories = new Map<string, string[]>()

  constructor() {
    this.initializeBuiltInRules()
  }

  /**
   * 注册验证规则
   */
  register(rule: ValidationRule): void {
    this.rules.set(rule.id, { ...rule })
    
    // 按类型分类
    const category = rule.type
    if (!this.ruleCategories.has(category)) {
      this.ruleCategories.set(category, [])
    }
    this.ruleCategories.get(category)!.push(rule.id)
  }

  /**
   * 注销验证规则
   */
  unregister(ruleId: string): void {
    const rule = this.rules.get(ruleId)
    if (rule) {
      this.rules.delete(ruleId)
      
      // 从分类中移除
      const category = rule.type
      const categoryRules = this.ruleCategories.get(category)
      if (categoryRules) {
        const index = categoryRules.indexOf(ruleId)
        if (index > -1) {
          categoryRules.splice(index, 1)
        }
      }
    }
  }

  /**
   * 获取验证规则
   */
  get(ruleId: string): ValidationRule | undefined {
    return this.rules.get(ruleId)
  }

  /**
   * 获取所有验证规则
   */
  getAll(): ValidationRule[] {
    return Array.from(this.rules.values())
  }

  /**
   * 按类型获取验证规则
   */
  getByType(type: 'node' | 'edge' | 'flow'): ValidationRule[] {
    return this.getAll().filter(rule => rule.type === type)
  }

  /**
   * 获取启用的验证规则
   */
  getEnabled(): ValidationRule[] {
    return this.getAll().filter(rule => rule.enabled)
  }

  /**
   * 启用/禁用验证规则
   */
  setEnabled(ruleId: string, enabled: boolean): void {
    const rule = this.rules.get(ruleId)
    if (rule) {
      rule.enabled = enabled
    }
  }

  /**
   * 设置规则优先级
   */
  setPriority(ruleId: string, priority: number): void {
    const rule = this.rules.get(ruleId)
    if (rule) {
      rule.priority = priority
    }
  }

  /**
   * 执行验证
   */
  async validate(context: ValidationContext, ruleIds?: string[]): Promise<ValidationResult[]> {
    const rulesToExecute = ruleIds 
      ? ruleIds.map(id => this.rules.get(id)).filter(Boolean) as ValidationRule[]
      : this.getEnabled()

    const results: ValidationResult[] = []

    for (const rule of rulesToExecute) {
      try {
        const result = await rule.validator(context)
        if (result) {
          results.push(result)
        }
      } catch (error) {
        console.error(`Validation rule ${rule.id} failed:`, error)
        results.push({
          id: `${rule.id}-error`,
          ruleId: rule.id,
          type: 'error',
          message: `验证规则执行失败: ${error}`
        })
      }
    }

    // 按优先级排序
    results.sort((a, b) => {
      const ruleA = this.rules.get(a.ruleId)
      const ruleB = this.rules.get(b.ruleId)
      return (ruleA?.priority || 999) - (ruleB?.priority || 999)
    })

    return results
  }

  /**
   * 批量注册规则
   */
  registerBatch(rules: ValidationRule[]): void {
    rules.forEach(rule => this.register(rule))
  }

  /**
   * 导出规则配置
   */
  exportConfig(): any {
    return {
      rules: this.getAll().map(rule => ({
        id: rule.id,
        name: rule.name,
        type: rule.type,
        enabled: rule.enabled,
        priority: rule.priority,
        description: rule.description
      }))
    }
  }

  /**
   * 导入规则配置
   */
  importConfig(config: any): void {
    if (config.rules && Array.isArray(config.rules)) {
      config.rules.forEach((ruleConfig: any) => {
        const rule = this.rules.get(ruleConfig.id)
        if (rule) {
          rule.enabled = ruleConfig.enabled ?? rule.enabled
          rule.priority = ruleConfig.priority ?? rule.priority
        }
      })
    }
  }

  /**
   * 初始化内置规则
   */
  private initializeBuiltInRules(): void {
    const builtInRules: ValidationRule[] = [
      {
        id: 'no-orphan-nodes',
        name: '无孤立节点',
        type: 'flow',
        validator: this.validateNoOrphanNodes,
        enabled: true,
        priority: 1,
        description: '检查是否存在没有连接的孤立节点'
      },
      {
        id: 'no-cycles',
        name: '无循环依赖',
        type: 'flow',
        validator: this.validateNoCycles,
        enabled: true,
        priority: 2,
        description: '检查流程中是否存在循环依赖'
      },
      {
        id: 'start-node-required',
        name: '必须有开始节点',
        type: 'flow',
        validator: this.validateStartNodeRequired,
        enabled: true,
        priority: 3,
        description: '流程必须包含至少一个开始节点'
      },
      {
        id: 'end-node-required',
        name: '必须有结束节点',
        type: 'flow',
        validator: this.validateEndNodeRequired,
        enabled: true,
        priority: 4,
        description: '流程必须包含至少一个结束节点'
      },
      {
        id: 'node-label-required',
        name: '节点标签必填',
        type: 'node',
        validator: this.validateNodeLabelRequired,
        enabled: true,
        priority: 5,
        description: '所有节点必须有标签'
      },
      {
        id: 'decision-node-branches',
        name: '判断节点分支检查',
        type: 'node',
        validator: this.validateDecisionNodeBranches,
        enabled: true,
        priority: 6,
        description: '判断节点必须有至少两个输出分支'
      },
      {
        id: 'node-size-validation',
        name: '节点大小验证',
        type: 'node',
        validator: this.validateNodeSize,
        enabled: true,
        priority: 7,
        description: '节点大小必须在合理范围内'
      },
      {
        id: 'edge-connection-validation',
        name: '连线有效性验证',
        type: 'edge',
        validator: this.validateEdgeConnection,
        enabled: true,
        priority: 8,
        description: '验证连线的源节点和目标节点是否有效'
      }
    ]

    this.registerBatch(builtInRules)
  }

  // 验证规则实现
  private validateNoOrphanNodes = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { allNodes, allEdges } = context
    const connectedNodes = new Set<string>()

    allEdges.forEach(edge => {
      connectedNodes.add(edge.source)
      connectedNodes.add(edge.target)
    })

    const orphanNodes = allNodes.filter(node => !connectedNodes.has(node.id))

    if (orphanNodes.length > 0) {
      return {
        id: 'orphan-nodes',
        ruleId: 'no-orphan-nodes',
        type: 'warning',
        message: `发现 ${orphanNodes.length} 个孤立节点: ${orphanNodes.map(n => n.label).join(', ')}`,
        suggestions: ['连接孤立节点到流程中', '删除不需要的节点']
      }
    }

    return null
  }

  private validateNoCycles = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { allEdges } = context
    
    // 简单的循环检测算法
    const graph = new Map<string, string[]>()
    allEdges.forEach(edge => {
      if (!graph.has(edge.source)) {
        graph.set(edge.source, [])
      }
      graph.get(edge.source)!.push(edge.target)
    })

    const visited = new Set<string>()
    const recursionStack = new Set<string>()

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) return true
      if (visited.has(node)) return false

      visited.add(node)
      recursionStack.add(node)

      const neighbors = graph.get(node) || []
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor)) return true
      }

      recursionStack.delete(node)
      return false
    }

    for (const node of graph.keys()) {
      if (!visited.has(node) && hasCycle(node)) {
        return {
          id: 'cycle-detected',
          ruleId: 'no-cycles',
          type: 'error',
          message: '流程中存在循环依赖',
          suggestions: ['检查节点连接', '移除形成循环的连线']
        }
      }
    }

    return null
  }

  private validateStartNodeRequired = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { allNodes } = context
    const startNodes = allNodes.filter(node => node.type === 'start')

    if (startNodes.length === 0) {
      return {
        id: 'no-start-node',
        ruleId: 'start-node-required',
        type: 'error',
        message: '流程必须包含至少一个开始节点',
        suggestions: ['添加开始节点']
      }
    }

    return null
  }

  private validateEndNodeRequired = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { allNodes } = context
    const endNodes = allNodes.filter(node => node.type === 'end')

    if (endNodes.length === 0) {
      return {
        id: 'no-end-node',
        ruleId: 'end-node-required',
        type: 'error',
        message: '流程必须包含至少一个结束节点',
        suggestions: ['添加结束节点']
      }
    }

    return null
  }

  private validateNodeLabelRequired = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { node } = context
    if (!node) return null

    if (!node.label || node.label.trim() === '') {
      return {
        id: `node-${node.id}-no-label`,
        ruleId: 'node-label-required',
        type: 'warning',
        message: `节点 "${node.id}" 缺少标签`,
        target: { type: 'node', id: node.id },
        suggestions: ['为节点添加描述性标签']
      }
    }

    return null
  }

  private validateDecisionNodeBranches = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { node, allEdges } = context
    if (!node || node.type !== 'decision') return null

    const outgoingEdges = allEdges.filter(edge => edge.source === node.id)

    if (outgoingEdges.length < 2) {
      return {
        id: `decision-${node.id}-insufficient-branches`,
        ruleId: 'decision-node-branches',
        type: 'warning',
        message: `判断节点 "${node.label}" 应该有至少两个输出分支`,
        target: { type: 'node', id: node.id },
        suggestions: ['添加更多输出连线', '检查判断逻辑']
      }
    }

    return null
  }

  private validateNodeSize = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { node } = context
    if (!node) return null

    const minWidth = 50
    const minHeight = 30
    const maxWidth = 500
    const maxHeight = 300

    if (node.size.width < minWidth || node.size.height < minHeight) {
      return {
        id: `node-${node.id}-too-small`,
        ruleId: 'node-size-validation',
        type: 'warning',
        message: `节点 "${node.label}" 尺寸过小`,
        target: { type: 'node', id: node.id },
        suggestions: [`最小尺寸: ${minWidth}x${minHeight}`]
      }
    }

    if (node.size.width > maxWidth || node.size.height > maxHeight) {
      return {
        id: `node-${node.id}-too-large`,
        ruleId: 'node-size-validation',
        type: 'warning',
        message: `节点 "${node.label}" 尺寸过大`,
        target: { type: 'node', id: node.id },
        suggestions: [`最大尺寸: ${maxWidth}x${maxHeight}`]
      }
    }

    return null
  }

  private validateEdgeConnection = async (context: ValidationContext): Promise<ValidationResult | null> => {
    const { edge, allNodes } = context
    if (!edge) return null

    const sourceNode = allNodes.find(n => n.id === edge.source)
    const targetNode = allNodes.find(n => n.id === edge.target)

    if (!sourceNode) {
      return {
        id: `edge-${edge.id}-invalid-source`,
        ruleId: 'edge-connection-validation',
        type: 'error',
        message: `连线 "${edge.id}" 的源节点不存在`,
        target: { type: 'edge', id: edge.id },
        suggestions: ['检查源节点是否已删除', '重新连接到有效节点']
      }
    }

    if (!targetNode) {
      return {
        id: `edge-${edge.id}-invalid-target`,
        ruleId: 'edge-connection-validation',
        type: 'error',
        message: `连线 "${edge.id}" 的目标节点不存在`,
        target: { type: 'edge', id: edge.id },
        suggestions: ['检查目标节点是否已删除', '重新连接到有效节点']
      }
    }

    return null
  }
}

// 全局验证规则管理器实例
export const validationRuleManager = new ValidationRuleManager()
