<template>
  <v-stage :config="configKonva">
    <v-layer>
      <v-circle :config="configCircle" @click="handleClick($event)"></v-circle>
    </v-layer>
  </v-stage>
  <div @click.alt.prevent="handleClick" @keydown.enter.prevent="handleClick">testtesttesttesttesttesttest</div>
</template>

<script setup lang="ts">

const configKonva = {
  width: 200,
  height: 200
}

const configCircle = {
  x: 100,
  y: 100,
  radius: 70,
  fill: "red",
  stroke: "black",
  strokeWidth: 4
}
const handleClick = (e: any) => {
  console.log('%c [ e ]-25', 'font-size:13px; background:pink; color:#bf2c9f;', e)
  console.log("clicked")
}
</script>

<style scoped></style>