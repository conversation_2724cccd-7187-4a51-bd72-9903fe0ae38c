{"name": "bpmn-vue-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "test": "set NODE_ENV=local&& node ./test.js", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "@bpmn-io/properties-panel": "^3.18.2", "@infectoone/vue-ganttastic": "^2.3.2", "@vueuse/components": "^10.9.0", "@vueuse/core": "^10.9.0", "axios": "^1.6.8", "bpmn-js": "^17.2.1", "bpmn-js-properties-panel": "^5.14.0", "bpmn-js-token-simulation": "^0.33.2", "camunda-bpmn-moddle": "^7.0.1", "chart.js": "^4.4.4", "dayjs": "^1.11.13", "echarts": "^5.5.1", "inversify": "^6.0.2", "json-editor-vue3": "^1.1.1", "konva": "^9.3.6", "leader-line": "^1.0.7", "loadash": "^1.0.0", "lodash": "^4.17.21", "min-dom": "^5.0.0", "perfect-scrollbar": "^1.5.5", "pinia": "^2.1.7", "primeicons": "^7.0.0", "primevue": "^3.52.0", "sass": "^1.72.0", "vue": "^3.4.21", "vue-json-editor": "^1.4.3", "vue-konva": "^3.0.2", "vue3-cron-plus-picker": "^1.0.2", "vue3-ts-jsoneditor": "^2.11.2"}, "devDependencies": {"@arco-design/web-vue": "^2.56.1", "@rollup/plugin-commonjs": "^25.0.7", "@types/inherits": "^0.0.33", "@types/lodash": "^4.17.0", "@types/node": "^20.12.2", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.21", "tailwindcss": "^3.4.13", "typescript": "^5.2.2", "vite": "^5.2.0", "vue-tsc": "^2.0.6"}}