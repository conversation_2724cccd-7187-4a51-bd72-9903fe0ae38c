import { BaseNode } from './BaseNode'
import type { ValidationRule } from '../types'

/**
 * 开始节点
 */
export class StartNode extends BaseNode {
  constructor() {
    super({
      id: 'start',
      name: '开始',
      category: 'control',
      icon: 'pi pi-play',
      color: '#4CAF50',
      description: '流程开始节点',
      defaultSize: { width: 100, height: 60 },
      allowedConnections: {
        input: [],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      triggerType: 'manual',
      autoStart: false
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 30,
      fill: this.color,
      stroke: '#388E3C',
      strokeWidth: 2
    }
  }

  getX6Shape(): string {
    return 'flow-circle'
  }

  protected createPorts() {
    return [
      {
        id: 'right',
        group: 'output' as const,
        position: { x: 0, y: 0 },
        label: '开始'
      }
    ]
  }
}

/**
 * 结束节点
 */
export class EndNode extends BaseNode {
  constructor() {
    super({
      id: 'end',
      name: '结束',
      category: 'control',
      icon: 'pi pi-stop',
      color: '#F44336',
      description: '流程结束节点',
      defaultSize: { width: 100, height: 60 },
      allowedConnections: {
        input: ['*'],
        output: []
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      returnCode: 0,
      message: '流程完成'
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 30,
      fill: this.color,
      stroke: '#D32F2F',
      strokeWidth: 2
    }
  }

  getX6Shape(): string {
    return 'flow-circle'
  }

  protected createPorts() {
    return [
      {
        id: 'left',
        group: 'input' as const,
        position: { x: 0, y: 0 },
        label: '结束'
      }
    ]
  }
}

/**
 * 判断节点
 */
export class DecisionNode extends BaseNode {
  constructor() {
    super({
      id: 'decision',
      name: '判断',
      category: 'control',
      icon: 'pi pi-question',
      color: '#FF9800',
      description: '条件判断节点',
      defaultSize: { width: 100, height: 100 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      conditions: [],
      operator: 'AND',
      defaultBranch: 'false'
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      fill: this.color,
      stroke: '#F57C00',
      strokeWidth: 2
    }
  }

  getX6Shape(): string {
    return 'flow-polygon'
  }

  getValidationRules(): ValidationRule[] {
    return [
      {
        id: 'decision-branches-required',
        name: '判断节点分支检查',
        type: 'node',
        validator: async (context) => {
          const { node, allEdges } = context
          if (!node || node.type !== 'decision') return null

          const outgoingEdges = allEdges.filter(edge => edge.source === node.id)
          if (outgoingEdges.length < 2) {
            return {
              id: `decision-${node.id}-insufficient-branches`,
              ruleId: 'decision-branches-required',
              type: 'warning',
              message: `判断节点 "${node.label}" 应该有至少两个输出分支`,
              target: { type: 'node', id: node.id },
              suggestions: ['添加更多输出连线', '检查判断逻辑']
            }
          }
          return null
        },
        enabled: true,
        priority: 6,
        description: '判断节点必须有至少两个输出分支'
      }
    ]
  }
}

/**
 * 并行节点
 */
export class ParallelNode extends BaseNode {
  constructor() {
    super({
      id: 'parallel',
      name: '并行',
      category: 'control',
      icon: 'pi pi-clone',
      color: '#9C27B0',
      description: '并行处理节点',
      defaultSize: { width: 120, height: 60 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      maxConcurrency: 5,
      waitForAll: false,
      timeout: 300000
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 4,
      fill: this.color,
      stroke: '#7B1FA2',
      strokeWidth: 2
    }
  }
}

/**
 * 合并节点
 */
export class MergeNode extends BaseNode {
  constructor() {
    super({
      id: 'merge',
      name: '合并',
      category: 'control',
      icon: 'pi pi-sitemap',
      color: '#607D8B',
      description: '流程合并节点',
      defaultSize: { width: 120, height: 60 },
      allowedConnections: {
        input: ['*'],
        output: ['*']
      }
    })
  }

  getDefaultProps(): Record<string, any> {
    return {
      waitForAll: true,
      mergeStrategy: 'combine',
      timeout: 300000
    }
  }

  getDefaultStyle(): Record<string, any> {
    return {
      borderRadius: 4,
      fill: this.color,
      stroke: '#455A64',
      strokeWidth: 2
    }
  }
}
