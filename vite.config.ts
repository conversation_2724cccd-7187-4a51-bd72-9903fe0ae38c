import path from "path";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import commonjs from "@rollup/plugin-commonjs";
// 如何安装插件commonjs: npm install @rollup/plugin-commonjs --save-dev
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), commonjs()],
  // 配置路径别名 @ -> src
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src/"),
    },
  },
  // 配置代理服务器
  server: {
    proxy: {
      "/api": {
        target: "http://localhost:8080/",
        // target: "http://************:8080/",
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
});
