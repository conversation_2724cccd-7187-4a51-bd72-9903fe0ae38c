<template>
  <div class="refactored-drag-table">
    <!-- 表格容器 -->
    <div class="table-container">
      <table class="drag-table">
        <thead>
          <tr>
            <th class="drag-column">拖拽</th>
            <th
              v-for="column in columns"
              :key="column.field"
              :style="column.style"
            >
              {{ column.header }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in displayData"
            :key="getItemKey(item, index)"
            :class="getRowClasses(item, index)"
            :draggable="canDrag(item)"
            @dragstart="onDragStart($event, index)"
            @dragover="onDragOver($event)"
            @dragenter="onDragEnter($event, index)"
            @dragleave="onDragLeave($event, index)"
            @drop="onDrop($event, index)"
            @dragend="onDragEnd"
          >
            <!-- 拖拽手柄列 -->
            <td class="drag-handle-cell">
              <div class="drag-handle" :class="{ disabled: !canDrag(item) }">
                <span v-if="canDrag(item)" class="drag-icon">⋮⋮</span>
                <span v-else class="disabled-icon">🔒</span>
              </div>
            </td>

            <!-- 数据列 -->
            <td
              v-for="column in columns"
              :key="column.field"
              :style="column.style"
              :class="column.class"
            >
              <slot
                v-if="column.template"
                :name="column.template"
                :data="item"
                :index="index"
                :field="column.field"
              >
                {{ getFieldValue(item, column.field) }}
              </slot>
              <span v-else>{{ getFieldValue(item, column.field) }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 拖拽状态指示器 -->
    <div v-if="dragState.isDragging" class="drag-indicator">
      <span class="drag-indicator-text">
        正在拖拽: {{ getDraggedItemDisplay() }}
      </span>
    </div>

    <!-- 调试信息 (开发模式) -->
    <div v-if="debug && dragState.isDragging" class="debug-info">
      <p>
        拖拽源: {{ dragState.sourceIndex }} ({{
          dragState.draggedItem?.[displayField]
        }})
      </p>
      <p>当前悬停: {{ dragState.hoverIndex }}</p>
      <p>插入位置: {{ dragState.insertPosition }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useDragReorder } from "./hooks/useDragReorder";

// ==================== 类型定义 ====================
interface TableColumn {
  field: string;
  header: string;
  template?: string;
  style?: Record<string, any>;
  class?: string;
}

interface DragTableProps {
  data: any[];
  columns: TableColumn[];
  keyField?: string;
  displayField?: string;
  disabledField?: string;
  debug?: boolean;
}

interface DragTableEmits {
  (e: "update:data", value: any[]): void;
  (e: "row-reorder", event: { originalEvent: Event; value: any[] }): void;
}

// ==================== Props & Emits ====================
const props = withDefaults(defineProps<DragTableProps>(), {
  keyField: "id",
  displayField: "name",
  disabledField: "disabled",
  debug: false,
});

const emit = defineEmits<DragTableEmits>();

// ==================== 响应式数据 ====================
const displayData = ref<any[]>([]);

// ==================== 使用拖拽 Hook ====================
const {
  dragState,
  canDrag,
  getRowClasses,
  handleDragStart,
  handleDragOver,
  handleDragEnter,
  handleDragLeave,
  handleDrop,
  handleDragEnd,
} = useDragReorder({
  keyField: props.keyField,
  displayField: props.displayField,
  disabledField: props.disabledField,
  debug: props.debug,
});

// ==================== 数据同步 ====================
watch(
  () => props.data,
  (newData) => {
    displayData.value = [...newData];
  },
  { immediate: true, deep: true }
);

watch(
  displayData,
  (newData) => {
    emit("update:data", newData);
  },
  { deep: true }
);

// ==================== 工具函数 ====================
const getItemKey = (item: any, index: number): string => {
  return item[props.keyField]?.toString() || index.toString();
};

const getFieldValue = (item: any, field: string): any => {
  return field.split(".").reduce((obj, key) => obj?.[key], item) ?? "";
};

const getDraggedItemDisplay = (): string => {
  return dragState.value.draggedItem?.[props.displayField] || "未知项目";
};

// ==================== 事件处理包装函数 ====================
const onDragStart = (event: DragEvent, index: number) => {
  handleDragStart(event, index, displayData.value);
};

const onDragOver = (event: DragEvent) => {
  handleDragOver(event);
};

const onDragEnter = (event: DragEvent, index: number) => {
  handleDragEnter(event, index);
};

const onDragLeave = (event: DragEvent, index: number) => {
  handleDragLeave(event, index);
};

const onDrop = (event: DragEvent, targetIndex: number) => {
  const result = handleDrop(event, targetIndex, displayData.value);

  if (result) {
    displayData.value = result.newData;

    // 触发事件
    emit("row-reorder", {
      originalEvent: event,
      value: result.newData,
    });

    if (props.debug) {
      console.log(
        "重排序完成:",
        result.newData.map((item) => item[props.displayField])
      );
    }
  }
};

const onDragEnd = () => {
  handleDragEnd();
};
</script>

<style scoped>
/* ==================== 基础样式 ==================== */
.refactored-drag-table {
  position: relative;
  width: 100%;
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.drag-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  font-size: 14px;
}

/* ==================== 表头样式 ==================== */
.drag-table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
}

.drag-column {
  width: 60px;
  text-align: center;
}

/* ==================== 表格行样式 ==================== */
.drag-table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid #dee2e6;
}

.drag-table-row:hover {
  background-color: #f8f9fa;
}

.drag-table-row.disabled-row {
  background-color: #f5f5f5;
  opacity: 0.7;
}

.drag-table-row.disabled-row:hover {
  background-color: #f5f5f5;
}

/* ==================== 拖拽状态样式 ==================== */
.drag-table-row.dragging-source {
  opacity: 0.5;
  background-color: #e3f2fd;
}

.drag-table-row.drag-hover {
  background-color: #fff3cd;
}

.drag-table-row.insert-before {
  border-top: 3px solid #007bff;
}

.drag-table-row.insert-after {
  border-bottom: 3px solid #007bff;
}

/* ==================== 表格单元格样式 ==================== */
.drag-table td {
  padding: 12px 16px;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
}

.drag-handle-cell {
  width: 60px;
  text-align: center;
  padding: 8px;
}

/* ==================== 拖拽手柄样式 ==================== */
.drag-handle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.drag-handle.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.drag-icon {
  color: #6c757d;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: -2px;
}

.drag-handle:hover .drag-icon {
  color: #007bff;
}

.disabled-icon {
  color: #dc3545;
  font-size: 12px;
}

/* ==================== 拖拽指示器样式 ==================== */
.drag-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #007bff;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  z-index: 1000;
  animation: pulse 2s infinite;
}

.drag-indicator-text {
  font-size: 14px;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* ==================== 调试信息样式 ==================== */
.debug-info {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  z-index: 1000;
  min-width: 200px;
}

.debug-info p {
  margin: 4px 0;
}
</style>
