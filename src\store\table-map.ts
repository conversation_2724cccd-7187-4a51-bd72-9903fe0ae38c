import { defineStore } from "pinia";
import { toRaw } from "vue";

type TableData = {
  id: string;
  data: any;
};

type SourceTargetMap = {
  source: TableData;
  target: TableData;
};

type MapStore = {
  isDragging: boolean;
  sourceItem: string;
  targetItem: string;
  tableMaps: SourceTargetMap[];
};
const defaultState: MapStore = {
  isDragging: false,
  sourceItem: "",
  targetItem: "",
  tableMaps: [],
};

export default defineStore("tableMap", {
  state: (): MapStore => defaultState,
  getters: {},
  actions: {
    createLine(sourceId: string, targetId: string) {
      this.tableMaps.push({
        source: { id: sourceId, data: {} },
        target: { id: targetId, data: {} },
      });
    },
  },
});
