<template>
  <a-button @click="handleTestAdd">add</a-button>

  <div class="gantt bg-red-500">
    <div class="total-container">
      <slot name="left" :scope="{ viewWidth, taskTransformY, showGanttData }">
        <div class="task-info-container" :style="{ height: `${viewHeight}px` }">
          <div class="task-info-header">
            <div class="row-header">EQP ID</div>
            <div class="row-header">Detail</div>
          </div>


          <div class="task-body" :style="{ transform: `translateY(${taskTransformY}px)`, height: `${viewHeight}px` }">
            <div v-for="row in virtualColumns" :key="row.id" class="task-row" :style="{ height: `${itemHeight}px` }">
              <div class="task-item">{{ row.name }}</div>
              <div class="task-item">
                <IconOptions />
              </div>
            </div>
          </div>
        </div>
      </slot>
      <!-- <div class="gantt-container">
        <slot name="header">
          <TimeLine ref="timeLineRef" @wheel="handleTimelineScale" />
        </slot>

        <div id="gantt-body" class="gantt-body" :style="{ height: `${viewHeight}px`, width: `${viewWidth + 15}px` }"
          @scroll="handleScroll">
          <div class="gantt-row" v-for="row in showGanttData" :key="row.id" :style="{ width: `${rowWidth}px` }">
            <div class="gantt-task" v-for="task in row.list" :id="getTaskId(row.id, task)"
              :key="getTaskId(row.id, task)" @mouseenter="changeTooltipData({ rowId: row.id, ...task })"
              @mouseleave="changeTooltipData(null)" :style="getTaskStyle(task)">
            </div>
          </div>
        </div>
      </div> -->
      <div class="gantt-container">
        <slot name="header">
          <TimeLine ref="timeLineRef" @wheel="handleTimelineScale" />
        </slot>
        <UseVirtualList :list="ganttData" :options="{ itemHeight: itemHeight }" height="400px" id="gantt-body"
          :style="{ width: `${viewWidth + 15}px` }" class="gantt-body" @scroll="handleScroll">
          <template #default="props">
            <div class="gantt-row" :key="props.data.id" :id="props.data.id"
              :style="{ width: `${rowWidth}px`, height: `${itemHeight}px` }">
              <div class="gantt-task" v-for="task in props.data.list" :id="getTaskId(props.data.id, task)"
                :key="getTaskId(props.data.id, task)" @mouseenter="changeTooltipData({ rowId: props.data.id, ...task })"
                @mouseleave="changeTooltipData(null)" :style="getTaskStyle(task)">
              </div>
            </div>
          </template>
        </UseVirtualList>
      </div>
    </div>
    <gantt-tooltip />

  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, toRef, provide, PropType, watch } from 'vue'
import GanttTooltip from './GanttTooltip.vue'
import dayjs from 'dayjs'
import { mockData } from './mockdata'
import { getTaskId, stepMap, } from './utils'
import type { ShowItem, GanttData, ShowGanttItem } from './utils'
import TimeLine from './TimeLine.vue'
import IconOptions from './IconOptions.vue'
import { UseVirtualList } from '@vueuse/components'

const props = defineProps({
  data: {
    type: Object as PropType<GanttData[]>,
    default: () => mockData,
    // required: true,
  },
  startDate: {
    type: String,
    default: '2022-01-01 19:00:00',
    // required: true,
  },
  endDate: {
    type: String,
    default: '2022-1-02 7:00:00',
    // required: true,
  },
  viewWidth: {
    type: Number,
    default: 1000,
    // required: true,
  },
  viewHeight: {
    type: Number,
    default: 400,
  },
  legend: {
    type: Object,
    default: () => ({
      style1: {
        color: '#75B798',
        hoverColor: '#0C753E',
      },
      style2: {
        color: '#6EA8FE',
        hoverColor: '#0A58CA ',
      },
      style3: {
        color: '#B595DD',
        hoverColor: '#6F42C1',
      },
    })
  },
  showDetailThreshold: {
    type: Number,
    default: 15 * 60 * 1000, // 15分钟
  }
})

const itemHeight = 28
const tooltipData = ref({})

const viewWidth = computed(() => {
  return props.viewWidth
})
const viewHeight = computed(() => {
  return props.viewHeight
})
const showNums = Math.floor(viewHeight.value / itemHeight)

const virtualStartIndex = ref(0)
const virtualEndIndex = ref(Math.floor(viewHeight.value / itemHeight) * 2 + 20)
const virtualColumns = computed(() => {
  const res = showGanttData.value.slice(virtualStartIndex.value, virtualEndIndex.value)
  return res
})

// 甘特图组件 传入数据  根据数据渲染甘特图

const getTaskStyle = (task: any) => {
  const res = `width: ${task.width}px; left: ${task.left}px; background-color: ${props.legend[task.legendKey].color};`
  return res
}

const originStartDateTime = toRef(props, 'startDate')
const originEndDateTime = toRef(props, 'endDate')
const originTimeRangeStamp = computed(() => { // 总的时间范围
  return dayjs(originEndDateTime.value).valueOf() - dayjs(originStartDateTime.value).valueOf()
})


const rowWidth = computed(() => {
  // 计算每一行的宽度 以 props.viewWidth 和 timeRangeStamp为基准 原始的timeRangeStamp时间长度为props.viewWidth 这么长
  const res = (originTimeRangeStamp.value / timeRangeStamp.value) * viewWidth.value
  return res
})

// 计算传入数据的时间范围
const startDate = ref(dayjs(props.startDate as string).valueOf())
const endDate = ref(dayjs(props.endDate as string).valueOf())
const timeRangeStamp = computed(() => { // 屏幕内显示的时间范围
  return endDate.value - startDate.value
})

const stepTime = computed(() => {
  let res = 1 * 60 * 60 * 1000;
  for (let i = 0; i < stepMap.length; i++) {
    if (timeRangeStamp.value >= stepMap[i].timeRange) {
      res = stepMap[i].step
      break
    }
  }
  return res
})

const ganttData: any = ref([])

function formatTaskItem(taskItem: any) {
  // 清洗数据 过滤掉超出时间范围的任务
  if (dayjs(taskItem.startTime).valueOf() > dayjs(props.endDate).valueOf()) {
    return null
  }
  const endTimeValue = dayjs(taskItem.endTime).valueOf() > dayjs(props.endDate).valueOf() ? dayjs(props.endDate).valueOf() : dayjs(taskItem.endTime).valueOf()
  // 计算任务宽度
  const taskWidth = (endTimeValue - dayjs(taskItem.startTime).valueOf()) / timeRangeStamp.value * viewWidth.value
  // 计算任务开始位置
  const taskLeft = (dayjs(taskItem.startTime).valueOf() - dayjs(originStartDateTime.value).valueOf()) / timeRangeStamp.value * viewWidth.value
  return {
    ...taskItem,
    width: taskWidth,
    left: taskLeft,
  }
}

function formatTask(task: any) {
  const showItemList: any[] = []
  task.list.forEach((task: any) => {
    const res = formatTaskItem({ ...task } as any)
    showItemList.push(res)
  })
  return {
    id: task.id,
    name: task.name,
    list: showItemList.filter((item: any) => item),
  }
}

watch(() => props.data, (newVal) => {
  ganttData.value = newVal.map((task: any) => formatTask(task))
}, {
  immediate: true,
})

provide('ganttData', ganttData)
provide('startDate', startDate)
provide('endDate', endDate)
provide('timeRangeStamp', timeRangeStamp)
provide('originTimeRangeStamp', originTimeRangeStamp)
provide('stepTime', stepTime)
provide('viewWidth', viewWidth)
provide('rowWidth', rowWidth)
provide('tooltipData', tooltipData)

function changeStartTime(direction: string, step = 1) {
  const originTime = dayjs(originStartDateTime.value).valueOf()
  // 增减的时间默认步长为1小时
  const calcStepTime = step * 60 * 60 * 1000
  if (direction === "plus") {
    const newTime = startDate.value + calcStepTime
    if (newTime < endDate.value) {
      return startDate.value = newTime
    } else return false
  }
  if (direction === "minus") {
    const newTime = startDate.value - calcStepTime
    if (newTime >= originTime) {
      return startDate.value = newTime
    } else { // 如果减少后的时间小于原始时间 则变为原始时间 如果已经是原始时间 则返回false
      return newTime === originTime ? false : startDate.value = originTime
    }
  }
}

function changeEndTime(direction: string, step = 1) {
  const originTime = dayjs(originEndDateTime.value).valueOf()
  const calcStepTime = step * 60 * 60 * 1000
  if (direction === "plus") {
    const newTime = endDate.value + calcStepTime
    if (newTime <= originTime) {
      return endDate.value = newTime
    } else {
      return newTime === originTime ? false : endDate.value = originTime
    }
  }
  if (direction === "minus") {
    const newTime = endDate.value - calcStepTime
    if (newTime > startDate.value) {
      return endDate.value = newTime
    } else return false
  }
}
function scaleTimeLine(type: string, step = 1) {
  if (type === "zoomIn") {
    // changeStartTime("plus", step)
    changeEndTime("minus", step)
  } else if (type === "zoomOut") {
    // changeStartTime("minus", step)
    changeEndTime("plus", step)
  }
}


function changeTooltipData(data: any) {
  tooltipData.value = data
}
// 为了判断时候横向滚动 使用变量保存上一次的滚动位置
let lastScrollLeft = 0
let lastScrollTop = 0
const timeLineRef: any = ref(null)
const taskTransformY = ref(0)
function handleScroll(e: any) {
  const scrollLeft = e.target.scrollLeft
  // 判断方向
  if (lastScrollLeft === scrollLeft) { // 上下滑动
    const currentViewNum = Math.ceil(e.target.scrollTop / itemHeight)
    // 判断上下方向
    if (lastScrollTop < e.target.scrollTop) {
      // 下滑动
      const dangerNum = (virtualEndIndex.value - 20)
      if (dangerNum < currentViewNum) {
        virtualStartIndex.value = Math.max(virtualStartIndex.value, currentViewNum - showNums)
        virtualEndIndex.value = Math.min(ganttData.value.length, currentViewNum + showNums * 2 + 20)
      }
    } else {
      // 上滑动
      const dangerNum = virtualStartIndex.value + 20
      if (dangerNum > currentViewNum) {
        virtualStartIndex.value = Math.max(0, currentViewNum - showNums)
        virtualEndIndex.value = Math.min(ganttData.value.length, currentViewNum + showNums * 2 + 20)
      }
    }
    const calcTransformY = (e.target.scrollTop - virtualStartIndex.value * itemHeight) * -1
    lastScrollTop = e.target.scrollTop

    return taskTransformY.value = calcTransformY

  } else {
    lastScrollLeft = scrollLeft
    timeLineRef.value.setTransformX(-scrollLeft)
  }
}

function handleTimelineScale(e: any) {
  if (e.deltaY > 0) {
    scaleTimeLine('zoomIn')
  } else {
    scaleTimeLine('zoomOut')
  }
}
const showGanttData = computed(() => {
  return ganttData.value
})

let count = 1
function handleTestAdd() {
  const testData = {
    "id": '3-' + count,
    "name": "任务3-" + count++,
    "list": [
      {
        "label": "子任务31",
        "startTime": "2022-01-01 19:55:00",
        "endTime": "2022-01-01 22:36:00",
        "legendKey": "style1",
        "detailList": [
          {
            "label": "子任务31",
            "startTime": "2022-01-01 19:55:00",
            "endTime": "2022-01-01 20:33:00",
            "legendKey": "style1"
          },
          {
            "label": "子任务31",
            "startTime": "2022-01-01 20:33:00",
            "endTime": "2022-01-01 21:19:00",
            "legendKey": "style1"
          },
          {
            "label": "子任务31",
            "startTime": "2022-01-01 21:19:00",
            "endTime": "2022-01-01 22:36:00",
            "legendKey": "style1"
          }
        ]
      },
      {
        "label": "子任务32",
        "startTime": "2022-01-02 00:31:00",
        "endTime": "2022-01-02 01:15:00",
        "legendKey": "style2"
      },
      {
        "label": "子任务33",
        "startTime": "2022-01-02 04:57:00",
        "endTime": "2022-01-02 06:19:00",
        "legendKey": "style3"
      }
    ]
  }
  // 插入到第三条数据后
  ganttData.value.splice(3, 0, formatTask(testData))
  ganttData.value = [...ganttData.value]
}

onMounted(() => {
  // const ps = new PerfectScrollbar('#gantt-body');
})
</script>

<style lang="scss">
.gantt {
  width: 100%;
  color: black;
  // height: 100vh;
  background-color: aqua;
  display: flex;
  justify-content: center;
  // align-items: end; // flex-direction: column;

  .total-container {
    display: flex;
    justify-content: center;
    align-items: end;
    background: #DEE2E6;
    border-radius: 14px 14px 0 0;
    position: relative;
    font-size: 12px;
    color: #212529;
  }

  .task-body {
    ::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }
  }

  #gantt-body {
    position: relative;
  }

  .time-line {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    z-index: 1;

    .time-line-item {
      height: 20px;
      text-align: left;
    }
  }
}

.task-info-container {
  width: 100px;
  background-color: #f5f5f5;
  overflow: hidden;
  box-sizing: content-box;

  .task-info-header {
    height: 30px;
    position: absolute;
    width: 100px;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .row-header {
      width: 60px;
      height: 100%;
      text-align: center;
      border-right: 1px solid #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .task-row {
    border-top: 1px solid #ccc;
    height: 28px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

    // box-sizing: content-box;
    .task-item {
      // height: 100%;
      width: 60px
    }
  }

}

.gantt-container {
  display: flex;
  flex-direction: column;
}

.gantt-body {
  box-sizing: content-box;
  overflow: auto;

  // overflow-x: hidden;
  .gantt-row {
    position: relative;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid #ccc;
    background-color: #f5f5f5;
    color: red;
  }

  .gantt-task {
    height: 16px;
    // border-radius: 100px 100px 100px 100px;
    position: absolute;

    &::after {
      content: "";
      position: absolute;
      right: 0;
      width: 1px;
      height: 100%;
      background-color: #fff;
    }
  }
}
</style>