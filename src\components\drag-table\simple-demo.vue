<template>
  <div class="simple-demo">
    <h2>可拖拽表格演示</h2>
    <div class="instructions">
      <h3>使用说明：</h3>
      <ul>
        <li>原始顺序：1, 2, 3(禁用), 4(禁用), 5, 6</li>
        <li>禁用的行（3和4）位置永远不变</li>
        <li>只能拖拽启用的行，不能拖拽到禁用行上</li>
        <li>
          测试：将项目1拖拽到项目4后面，预期结果应该是：2, 5, 3(禁用), 4(禁用),
          1, 6
        </li>
      </ul>
    </div>

    <div class="test-table">
      <table class="basic-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>名称</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in testData"
            :key="item.id"
            :class="{ 'disabled-row': item.disabled }"
            :draggable="!item.disabled"
            @dragstart="handleDragStart($event, index)"
            @dragover="handleDragOver"
            @drop="handleDrop($event, index)"
            @dragend="handleDragEnd"
          >
            <td>{{ item.id }}</td>
            <td>{{ item.name }}</td>
            <td>
              <span
                :class="item.disabled ? 'status-disabled' : 'status-enabled'"
              >
                {{ item.disabled ? "禁用" : "启用" }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="controls">
      <button @click="resetData" class="reset-btn">重置数据</button>
    </div>

    <div class="result">
      <h3>当前数据顺序：</h3>
      <ul>
        <li v-for="item in testData" :key="item.id">
          {{ item.id }} - {{ item.name }} ({{
            item.disabled ? "禁用" : "启用"
          }})
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const testData = ref([
  { id: 1, name: "项目1", disabled: false },
  { id: 2, name: "项目2", disabled: false },
  { id: 3, name: "项目3", disabled: true },
  { id: 4, name: "项目4", disabled: true },
  { id: 5, name: "项目5", disabled: false },
  { id: 6, name: "项目6", disabled: false },
]);

let dragIndex = -1;

const handleDragStart = (e: DragEvent, index: number) => {
  if (testData.value[index].disabled) {
    e.preventDefault();
    return;
  }
  dragIndex = index;
  console.log("开始拖拽:", index, testData.value[index]);
};

const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
};

const handleDrop = (e: DragEvent, dropIndex: number) => {
  e.preventDefault();

  if (testData.value[dropIndex].disabled) {
    console.log("不能拖拽到禁用行");
    return;
  }

  if (dragIndex !== -1 && dragIndex !== dropIndex) {
    console.log("拖拽从", dragIndex, "到", dropIndex);

    // 实现disabled行保持位置不变的逻辑
    const newData = reorderWithDisabledRows(dragIndex, dropIndex);
    testData.value = newData;
    console.log(
      "新顺序:",
      testData.value.map((item) => item.name)
    );
  }

  dragIndex = -1;
};

// 核心重排逻辑：确保disabled行位置不变
const reorderWithDisabledRows = (fromIndex: number, toIndex: number) => {
  const originalData = [...testData.value];
  const draggedItem = originalData[fromIndex];

  // 记录所有disabled行的原始位置和数据
  const disabledItems: { item: any; originalIndex: number }[] = [];
  originalData.forEach((item, index) => {
    if (item.disabled) {
      disabledItems.push({ item, originalIndex: index });
    }
  });

  // 获取所有enabled项目
  const enabledItems = originalData.filter((item) => !item.disabled);

  // 从enabled项目中移除被拖拽的项目
  const draggedEnabledIndex = enabledItems.findIndex(
    (item) => item.id === draggedItem.id
  );
  enabledItems.splice(draggedEnabledIndex, 1);

  // 计算目标位置在enabled项目中的索引
  let targetEnabledIndex = 0;
  for (let i = 0; i < toIndex; i++) {
    if (!originalData[i].disabled) {
      targetEnabledIndex++;
    }
  }

  // 如果拖拽方向是向下，需要调整索引
  if (fromIndex < toIndex) {
    targetEnabledIndex--;
  }

  // 在enabled项目中插入拖拽的项目
  enabledItems.splice(targetEnabledIndex, 0, draggedItem);

  // 重新构建最终数组，将disabled项目放回原位置
  const finalData: any[] = [];
  let enabledIndex = 0;

  for (let i = 0; i < originalData.length; i++) {
    const disabledItem = disabledItems.find((d) => d.originalIndex === i);
    if (disabledItem) {
      // 在原位置插入disabled项目
      finalData.push(disabledItem.item);
    } else {
      // 插入enabled项目
      if (enabledIndex < enabledItems.length) {
        finalData.push(enabledItems[enabledIndex]);
        enabledIndex++;
      }
    }
  }

  return finalData;
};

const handleDragEnd = () => {
  dragIndex = -1;
};

const resetData = () => {
  testData.value = [
    { id: 1, name: "项目1", disabled: false },
    { id: 2, name: "项目2", disabled: false },
    { id: 3, name: "项目3", disabled: true },
    { id: 4, name: "项目4", disabled: true },
    { id: 5, name: "项目5", disabled: false },
    { id: 6, name: "项目6", disabled: false },
  ];
  console.log("数据已重置");
};
</script>

<style scoped>
.simple-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.instructions {
  background-color: #e3f2fd;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #2196f3;
}

.instructions h3 {
  margin-top: 0;
  color: #1976d2;
}

.instructions ul {
  margin-bottom: 0;
}

.instructions li {
  margin-bottom: 8px;
  color: #424242;
}

.controls {
  margin: 20px 0;
  text-align: center;
}

.reset-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.reset-btn:hover {
  background-color: #45a049;
}

.basic-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.basic-table th,
.basic-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.basic-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.basic-table tr {
  cursor: grab;
  transition: background-color 0.2s;
}

.basic-table tr:hover {
  background-color: #f9f9f9;
}

.basic-table tr:active {
  cursor: grabbing;
}

.disabled-row {
  opacity: 0.6;
  cursor: not-allowed !important;
  background-color: #f0f0f0;
}

.disabled-row:hover {
  background-color: #f0f0f0 !important;
}

.status-enabled {
  color: #28a745;
  font-weight: bold;
}

.status-disabled {
  color: #dc3545;
  font-weight: bold;
}

.result {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.result ul {
  list-style-type: none;
  padding: 0;
}

.result li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}
</style>
