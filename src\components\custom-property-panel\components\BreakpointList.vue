<template>
  <div v-for="item of breakPointList" :key="item.id" class="breakpoint-item" @click="changeActiveElement(item.id)">
    <!-- <div :class="item.id === modelerStore.activeElementId ? 'active' : ''" style="width: 100%; display: inline-block;"> -->
    <div class="status-icon" :style="item.id === modelerStore.activeElementId ? 'background-color: green;' : ''"></div>
    <div class="content">{{ item.id }}</div>
    <icon-close class="delete-icon" v-if="!modelerStore.isRunning" @click.prevent="removeBreakpoint(item)" />
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import useModelerStore from '@/store/modeler'
import { debugApi } from '@/api'
import { IconClose } from '@arco-design/web-vue/es/icon';
import { remove as removeArrayElement } from 'lodash'

const modelerStore = useModelerStore()

const breakPointList = computed(() => {
  return modelerStore.breakpoints.map((item) => ({
    id: item.elementId,
    businessObject: item.businessObject
  }));
})

function changeActiveElement(id: string) {
  if (!modelerStore.overlayerManager[id]?.variables) return
  modelerStore.setActiveElement(id)
}

async function removeBreakpoint(element: any) {
  const htmlId = `overlay-break-point-${element.id}`
  const overlayHtml = document.getElementById(htmlId)
  removeArrayElement(modelerStore.breakpoints, (item: any) => item.elementId === element.id)
  await debugApi.setBreakPoints({
    breakpoints: modelerStore.breakpoints
  })
  modelerStore.overlayerManager[element.id].isDebugNode = false
  overlayHtml?.classList.remove('pause-node')
}
</script>

<style lang="scss">
.breakpoint-info {
  .breakpoint-item {
    cursor: pointer;
    width: 100%;
    // border: 1px solid #ccc;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 6px;
    box-sizing: border-box;

    // 设置点击悬浮样式
    &:hover {
      background-color: #f5f5f5;
    }

    &:active {
      background-color: #e6f7ff;
    }

    .active {
      background-color: rgb(240, 232, 131);
    }

    .status-icon {
      width: 24px;
      height: 24px;
      background-color: red;
      border: 3px solid white;
      border-radius: 50%;
      box-shadow: 2px 2px 5px grey;
    }

    .content {
      flex: 1;
    }

    .delete-icon {
      // position: absolute;
      // right: auto;
      font-size: 24px;
      color: red;

      &:hover {
        scale: 1.2;
      }

      &:active {
        scale: 0.9;
      }
    }
  }

  .arco-collapse-item-content {
    padding: 0px;
  }
}
</style>
