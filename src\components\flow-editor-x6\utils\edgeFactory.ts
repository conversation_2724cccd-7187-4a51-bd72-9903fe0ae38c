import type { FlowEdge, FlowNode } from '../types'
import { generateId } from './common'

/**
 * 创建边
 */
export function createEdge(
  source: string,
  target: string,
  options: {
    sourcePort?: string
    targetPort?: string
    label?: string
    data?: Record<string, any>
    style?: Record<string, any>
  } = {}
): FlowEdge {
  const {
    sourcePort,
    targetPort,
    label = '',
    data = {},
    style = {}
  } = options

  return {
    id: generateId('edge'),
    source,
    target,
    sourcePort,
    targetPort,
    label,
    data: { ...data },
    style: {
      stroke: '#333333',
      strokeWidth: 1,
      ...style
    },
    validationState: 'valid',
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }
}

/**
 * 克隆边
 */
export function cloneEdge(edge: FlowEdge, newSource?: string, newTarget?: string): FlowEdge {
  return {
    ...edge,
    id: generateId('edge'),
    source: newSource || edge.source,
    target: newTarget || edge.target,
    data: { ...edge.data },
    style: { ...edge.style },
    metadata: {
      ...edge.metadata,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      clonedFrom: edge.id
    }
  }
}

/**
 * 批量创建边
 */
export function createEdges(
  configs: Array<{
    source: string
    target: string
    options?: Parameters<typeof createEdge>[2]
  }>
): FlowEdge[] {
  return configs.map(config => createEdge(config.source, config.target, config.options))
}

/**
 * 验证边数据
 */
export function validateEdgeData(edge: FlowEdge): string[] {
  const errors: string[] = []

  if (!edge.id) {
    errors.push('Edge ID is required')
  }

  if (!edge.source) {
    errors.push('Edge source is required')
  }

  if (!edge.target) {
    errors.push('Edge target is required')
  }

  if (edge.source === edge.target) {
    errors.push('Edge source and target cannot be the same')
  }

  return errors
}

/**
 * 检查边是否形成循环
 */
export function detectCycle(edges: FlowEdge[], newEdge?: FlowEdge): boolean {
  const allEdges = newEdge ? [...edges, newEdge] : edges
  const graph = new Map<string, string[]>()

  // 构建邻接表
  allEdges.forEach(edge => {
    if (!graph.has(edge.source)) {
      graph.set(edge.source, [])
    }
    graph.get(edge.source)!.push(edge.target)
  })

  // DFS检测循环
  const visited = new Set<string>()
  const recursionStack = new Set<string>()

  function dfs(node: string): boolean {
    if (recursionStack.has(node)) {
      return true // 发现循环
    }

    if (visited.has(node)) {
      return false
    }

    visited.add(node)
    recursionStack.add(node)

    const neighbors = graph.get(node) || []
    for (const neighbor of neighbors) {
      if (dfs(neighbor)) {
        return true
      }
    }

    recursionStack.delete(node)
    return false
  }

  // 检查所有节点
  for (const node of graph.keys()) {
    if (!visited.has(node)) {
      if (dfs(node)) {
        return true
      }
    }
  }

  return false
}

/**
 * 获取节点的所有输入边
 */
export function getIncomingEdges(nodeId: string, edges: FlowEdge[]): FlowEdge[] {
  return edges.filter(edge => edge.target === nodeId)
}

/**
 * 获取节点的所有输出边
 */
export function getOutgoingEdges(nodeId: string, edges: FlowEdge[]): FlowEdge[] {
  return edges.filter(edge => edge.source === nodeId)
}

/**
 * 获取连接两个节点的边
 */
export function getEdgesBetweenNodes(sourceId: string, targetId: string, edges: FlowEdge[]): FlowEdge[] {
  return edges.filter(edge => edge.source === sourceId && edge.target === targetId)
}

/**
 * 检查两个节点是否已连接
 */
export function areNodesConnected(sourceId: string, targetId: string, edges: FlowEdge[]): boolean {
  return edges.some(edge => edge.source === sourceId && edge.target === targetId)
}

/**
 * 获取节点的所有邻居节点
 */
export function getNeighborNodes(nodeId: string, edges: FlowEdge[]): {
  incoming: string[]
  outgoing: string[]
  all: string[]
} {
  const incoming = getIncomingEdges(nodeId, edges).map(edge => edge.source)
  const outgoing = getOutgoingEdges(nodeId, edges).map(edge => edge.target)
  const all = [...new Set([...incoming, ...outgoing])]

  return { incoming, outgoing, all }
}

/**
 * 拓扑排序
 */
export function topologicalSort(nodes: FlowNode[], edges: FlowEdge[]): string[] {
  const graph = new Map<string, string[]>()
  const inDegree = new Map<string, number>()

  // 初始化
  nodes.forEach(node => {
    graph.set(node.id, [])
    inDegree.set(node.id, 0)
  })

  // 构建图和计算入度
  edges.forEach(edge => {
    graph.get(edge.source)!.push(edge.target)
    inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1)
  })

  // Kahn算法
  const queue: string[] = []
  const result: string[] = []

  // 找到所有入度为0的节点
  inDegree.forEach((degree, nodeId) => {
    if (degree === 0) {
      queue.push(nodeId)
    }
  })

  while (queue.length > 0) {
    const current = queue.shift()!
    result.push(current)

    // 处理当前节点的所有邻居
    const neighbors = graph.get(current) || []
    neighbors.forEach(neighbor => {
      const newDegree = (inDegree.get(neighbor) || 0) - 1
      inDegree.set(neighbor, newDegree)
      
      if (newDegree === 0) {
        queue.push(neighbor)
      }
    })
  }

  // 如果结果长度不等于节点数量，说明存在循环
  if (result.length !== nodes.length) {
    throw new Error('Graph contains cycle, cannot perform topological sort')
  }

  return result
}

/**
 * 查找最短路径 (Dijkstra算法)
 */
export function findShortestPath(
  startNodeId: string,
  endNodeId: string,
  edges: FlowEdge[],
  getWeight: (edge: FlowEdge) => number = () => 1
): string[] | null {
  const graph = new Map<string, Array<{ target: string; weight: number }>>()
  const distances = new Map<string, number>()
  const previous = new Map<string, string | null>()
  const unvisited = new Set<string>()

  // 构建图
  edges.forEach(edge => {
    if (!graph.has(edge.source)) {
      graph.set(edge.source, [])
    }
    graph.get(edge.source)!.push({
      target: edge.target,
      weight: getWeight(edge)
    })

    // 添加节点到未访问集合
    unvisited.add(edge.source)
    unvisited.add(edge.target)
  })

  // 初始化距离
  unvisited.forEach(nodeId => {
    distances.set(nodeId, nodeId === startNodeId ? 0 : Infinity)
    previous.set(nodeId, null)
  })

  while (unvisited.size > 0) {
    // 找到距离最小的未访问节点
    let current: string | null = null
    let minDistance = Infinity

    unvisited.forEach(nodeId => {
      const distance = distances.get(nodeId)!
      if (distance < minDistance) {
        minDistance = distance
        current = nodeId
      }
    })

    if (current === null || minDistance === Infinity) {
      break // 无法到达
    }

    unvisited.delete(current)

    if (current === endNodeId) {
      break // 找到目标
    }

    // 更新邻居距离
    const neighbors = graph.get(current) || []
    neighbors.forEach(({ target, weight }) => {
      if (unvisited.has(target)) {
        const newDistance = distances.get(current)! + weight
        if (newDistance < distances.get(target)!) {
          distances.set(target, newDistance)
          previous.set(target, current)
        }
      }
    })
  }

  // 重构路径
  if (distances.get(endNodeId) === Infinity) {
    return null // 无路径
  }

  const path: string[] = []
  let current: string | null = endNodeId

  while (current !== null) {
    path.unshift(current)
    current = previous.get(current)!
  }

  return path
}
