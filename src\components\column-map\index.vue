<template>
  <div class="container" @mousemove="showDashLine" ref="containerRef" @mouseup="hideDashLine">
    <Table v-for="(table, index) in props.tables" :key="table.tableName" :position="table.position"
      :table-name="table.tableName" :fields="table.fields" @start-connect="startConnect($event, table.tableName)"
      @end-connect="endConnect($event, table.tableName)" @move="handleTableMove">
      <template #row="{ item }">
        <slot name="row" :item="item" :table-name="table.tableName" :fields="table.fields"></slot>
      </template>
    </Table>
    <Lines @handleRightClick="handleRightClick" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, provide, PropType } from "vue";
import Table from "./table.vue";
import Lines from "./lines.vue";
import useFieldMap from "./use-field-map";
import { EmitData, FieldData, SourceTargetMap } from "./types";


const props = defineProps({
  tables: {
    type: Array<{
      tableName: string;
      position?: {
        x: number;
        y: number;
      };
      fields: Array<FieldData>;
    }>,
    default: () => [],
    requred: true,
  },
  initData: {
    type: Object as PropType<EmitData>,
    default: () => ({
      lines: [],
      directions: []
    }),
  }
});
const emits = defineEmits(["change", "line-right-click"]);
const { createLine, removeLine, tableMaps, startConnect, endConnect, lines,
  handleTableMove, currentLine, showDashLine, containerRef, hideDashLine, emitChange, tableDirctionMap } = useFieldMap(props, emits);

provide("tableDirctionMap", tableDirctionMap);
provide("lines", lines);
provide("currentLine", currentLine);


function addTableDirction(from: string, to: string) {
  tableDirctionMap.push({ from, to });
  emitChange()
}

function removeTableDirction(from: string, to: string) {
  tableDirctionMap.forEach((item, index) => {
    if (item.from === from && item.to === to) {
      tableDirctionMap.splice(index, 1);
    }
  });
  emitChange()
}

function handleRightClick(event: MouseEvent, line: any) {
  emits("line-right-click", event, line);
}

function getLineData() {
  return {
    tableMaps: tableMaps.value,
    tableDirctionMap: tableDirctionMap,
  }
}

onMounted(() => {
  tableMaps.value.push(...props.initData?.lines);
  tableDirctionMap.push(...props.initData?.directions);
});

defineExpose({
  createLine,
  removeLine,
  addTableDirction,
  removeTableDirction,
  getLineData
});
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  user-select: none;
  background-color: #bdf5df;

  .column {
    width: 100px;
    height: 100px;
    background-color: red;
    border: 3px solid black;
    position: absolute;

    .cursor-move {
      height: 50px;
      background-color: aqua;
      cursor: move;
    }
  }
}
</style>
