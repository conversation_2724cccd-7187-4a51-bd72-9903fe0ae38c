import { ref } from "vue";

// ==================== 类型定义 ====================
export interface DragState {
  isDragging: boolean;
  sourceIndex: number;
  hoverIndex: number;
  draggedItem: any;
  insertPosition: "before" | "after" | null;
}

export interface DragConfig {
  keyField: string;
  disabledField: string;
  displayField: string;
  debug?: boolean;
}

export interface ReorderResult {
  newData: any[];
  sourceItem: any;
  targetIndex: number;
  insertPosition: "before" | "after" | null;
}

// ==================== 拖拽重排序 Hook ====================
export function useDragReorder(config: DragConfig) {
  const { keyField, disabledField, displayField, debug = false } = config;

  // 拖拽状态
  const dragState = ref<DragState>({
    isDragging: false,
    sourceIndex: -1,
    hoverIndex: -1,
    draggedItem: null,
    insertPosition: null,
  });

  // ==================== 核心重排序算法 ====================
  /**
   * 新的重排序算法：
   * 1. 提取所有启用项目和禁用项目的位置信息
   * 2. 对启用项目进行重排序
   * 3. 将禁用项目按原位置插入回去
   */
  const performReorder = (
    data: any[],
    sourceIndex: number,
    targetIndex: number,
    insertPosition: "before" | "after" | null
  ): ReorderResult => {
    if (debug) {
      console.log("开始重排序:", { sourceIndex, targetIndex, insertPosition });
    }

    const originalData = [...data];
    const sourceItem = originalData[sourceIndex];

    // 步骤1: 记录禁用项目的原始位置
    const disabledItemsMap = new Map<number, any>();
    originalData.forEach((item, index) => {
      if (item[disabledField]) {
        disabledItemsMap.set(index, item);
      }
    });

    // 步骤2: 提取所有启用项目
    const enabledItems = originalData.filter((item) => !item[disabledField]);

    if (debug) {
      console.log(
        "启用项目:",
        enabledItems.map((item) => item[displayField])
      );
      console.log("禁用项目位置:", Array.from(disabledItemsMap.keys()));
    }

    // 步骤3: 计算源项目和目标项目在启用项目中的索引
    const sourceEnabledIndex = enabledItems.findIndex(
      (item) => item[keyField] === sourceItem[keyField]
    );

    // 计算目标在启用项目中的索引
    let targetEnabledIndex = calculateTargetEnabledIndex(
      originalData,
      targetIndex,
      insertPosition,
      disabledField
    );

    // 如果是向下拖拽且插入位置是before，需要调整索引
    if (sourceIndex < targetIndex && insertPosition === "before") {
      targetEnabledIndex = Math.max(0, targetEnabledIndex - 1);
    }

    if (debug) {
      console.log("启用项目索引:", { sourceEnabledIndex, targetEnabledIndex });
    }

    // 步骤4: 重排序启用项目
    const reorderedEnabledItems = reorderEnabledItems(
      enabledItems,
      sourceEnabledIndex,
      targetEnabledIndex
    );

    if (debug) {
      console.log(
        "重排序后启用项目:",
        reorderedEnabledItems.map((item) => item[displayField])
      );
    }

    // 步骤5: 重建最终数组（将禁用项目插入回原位置）
    const finalData = rebuildDataWithDisabledItems(
      reorderedEnabledItems,
      disabledItemsMap,
      originalData.length
    );

    if (debug) {
      console.log(
        "最终结果:",
        finalData.map((item) => item[displayField])
      );
    }

    return {
      newData: finalData,
      sourceItem,
      targetIndex,
      insertPosition,
    };
  };

  // ==================== 辅助函数 ====================

  /**
   * 计算目标位置在启用项目中的索引
   */
  const calculateTargetEnabledIndex = (
    originalData: any[],
    targetIndex: number,
    insertPosition: "before" | "after" | null,
    disabledField: string
  ): number => {
    let enabledIndex = 0;

    for (let i = 0; i < targetIndex; i++) {
      if (!originalData[i][disabledField]) {
        enabledIndex++;
      }
    }

    // 如果目标是禁用项目，需要特殊处理
    if (originalData[targetIndex][disabledField]) {
      if (insertPosition === "after") {
        // 拖拽到禁用项目下方，找到该禁用项目后面第一个启用项目的位置
        for (let i = targetIndex + 1; i < originalData.length; i++) {
          if (!originalData[i][disabledField]) {
            break;
          }
        }
      }
      // 如果是before，enabledIndex已经是正确的值
    } else {
      // 目标是启用项目
      if (insertPosition === "after") {
        enabledIndex++;
      }
    }

    return enabledIndex;
  };

  /**
   * 重排序启用项目数组
   */
  const reorderEnabledItems = (
    enabledItems: any[],
    sourceIndex: number,
    targetIndex: number
  ): any[] => {
    const items = [...enabledItems];
    const [movedItem] = items.splice(sourceIndex, 1);

    // 确保目标索引在有效范围内
    const insertIndex = Math.max(0, Math.min(targetIndex, items.length));
    items.splice(insertIndex, 0, movedItem);

    return items;
  };

  /**
   * 重建数据数组，将禁用项目插入回原位置
   */
  const rebuildDataWithDisabledItems = (
    enabledItems: any[],
    disabledItemsMap: Map<number, any>,
    originalLength: number
  ): any[] => {
    const result: any[] = [];
    let enabledIndex = 0;

    for (let i = 0; i < originalLength; i++) {
      if (disabledItemsMap.has(i)) {
        // 在原位置插入禁用项目
        result.push(disabledItemsMap.get(i));
      } else {
        // 插入重排序后的启用项目
        if (enabledIndex < enabledItems.length) {
          result.push(enabledItems[enabledIndex]);
          enabledIndex++;
        }
      }
    }

    return result;
  };

  // ==================== 拖拽事件处理 ====================

  const handleDragStart = (event: DragEvent, index: number, data: any[]) => {
    const item = data[index];

    if (item[disabledField]) {
      event.preventDefault();
      return false;
    }

    dragState.value = {
      isDragging: true,
      sourceIndex: index,
      hoverIndex: -1,
      draggedItem: item,
      insertPosition: null,
    };

    event.dataTransfer?.
    ("text/plain", index.toString());
    event.dataTransfer!.effectAllowed = "move";

    if (debug) {
      console.log("开始拖拽:", { index, item: item[displayField] });
    }

    return true;
  };

  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    event.dataTransfer!.dropEffect = "move";
  };

  const handleDragEnter = (event: DragEvent, index: number) => {
    event.preventDefault();

    if (!dragState.value.isDragging || index === dragState.value.sourceIndex) {
      return;
    }

    // 计算插入位置
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const mouseY = event.clientY;
    const rowMiddle = rect.top + rect.height / 2;
    const insertPosition = mouseY < rowMiddle ? "before" : "after";

    dragState.value.hoverIndex = index;
    dragState.value.insertPosition = insertPosition;

    if (debug) {
      console.log("拖拽进入:", { index, insertPosition });
    }
  };

  const handleDragLeave = (event: DragEvent, index: number) => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const mouseX = event.clientX;
    const mouseY = event.clientY;

    if (
      mouseX < rect.left ||
      mouseX > rect.right ||
      mouseY < rect.top ||
      mouseY > rect.bottom
    ) {
      if (dragState.value.hoverIndex === index) {
        dragState.value.hoverIndex = -1;
        dragState.value.insertPosition = null;
      }
    }
  };

  const handleDrop = (event: DragEvent, targetIndex: number, data: any[]) => {
    event.preventDefault();

    if (!dragState.value.isDragging) {
      return null;
    }

    const sourceIndex = dragState.value.sourceIndex;
    const insertPosition = dragState.value.insertPosition;

    if (debug) {
      console.log("拖拽放下:", {
        sourceIndex,
        targetIndex,
        insertPosition,
        sourceItem: dragState.value.draggedItem[displayField],
      });
    }

    // 执行重排序
    const result = performReorder(
      data,
      sourceIndex,
      targetIndex,
      insertPosition
    );

    return result;
  };

  const handleDragEnd = () => {
    if (debug) {
      console.log("拖拽结束");
    }

    dragState.value = {
      isDragging: false,
      sourceIndex: -1,
      hoverIndex: -1,
      draggedItem: null,
      insertPosition: null,
    };
  };

  // ==================== 辅助函数 ====================

  const canDrag = (item: any): boolean => {
    return !item[disabledField];
  };

  const getRowClasses = (item: any, index: number): string[] => {
    const classes: string[] = ["drag-table-row"];

    if (item[disabledField]) {
      classes.push("disabled-row");
    }

    if (dragState.value.isDragging) {
      if (index === dragState.value.sourceIndex) {
        classes.push("dragging-source");
      }
      if (index === dragState.value.hoverIndex) {
        classes.push("drag-hover");
        if (dragState.value.insertPosition) {
          classes.push(`insert-${dragState.value.insertPosition}`);
        }
      }
    }

    return classes;
  };

  return {
    // 状态
    dragState,

    // 计算属性
    canDrag,
    getRowClasses,

    // 方法
    performReorder,
    handleDragStart,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
  };
}
