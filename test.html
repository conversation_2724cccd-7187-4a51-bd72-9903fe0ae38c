<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>三角形边框矩形</title>
<style>
  .rectangle {
    width: 200px;
    height: 100px;
    background-color: #f0f0f0;
    position: relative;
    margin-left: 30px; /* 确保矩形右移，为左侧的三角形留出空间 */
  }

  .rectangle::before {
    content: '';
    position: absolute;
    left: -30px; /* 三角形的宽度 */
    border-radius: 5px;
    width: 100px;
    height: 100px;
    border: 10px solid #f0f0f0;
    border-radius: 5px;
    transform: rotate(45deg);
    /* top: 50%; */
  }
</style>
</head>
<body>
<div class="rectangle"></div>
</body>
</html>
