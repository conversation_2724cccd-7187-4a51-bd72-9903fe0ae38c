export function parseCronExpression(expression: string) {
  const arr = expression.split(" ");
  const resObj = {
    second: arr[0],
    minute: arr[1],
    hour: arr[2],
    day: arr[3],
    month: arr[4],
    week: arr[5],
    year: arr[6] || "",
  };
}

function parseCronValue(value: string) {
  // 解析cron表达式中的 , - * / 这些特殊字符
  if (value === "*") {
    return {
      type: "all",
      value: "1",
    };
  }
  if (value.includes(",")) {
    return {
      type: "list",
      value: "2",
      res: value.split(","),
    };
  }
  if (value.includes("/")) {
    return {
      type: "range",
      value: "3",
      res: value.split("/"),
    };
  }
  if (value.includes("-")) {
    return {
      type: "range",
      value: "4",
      res: value.split("-"),
    };
  }
  if (value === "?") {
    return {
      type: "any",
      value: "5",
    };
  }
  if (value.includes("L")) {
    return {
      type: "last",
      value: "6",
      res: value.split("L")[0],
    };
  }
  if (value.includes("W")) {
    return {
      type: "weekday",
      value: "7",
      res: value.split("W")[0],
    };
  }
  if (value.includes("#")) {
    return {
      type: "NthWeekdayOfMonth",
      value: "8",
      res: value.split("#")[0],
    };
  }
  if (value === "LW") {
    return {
      type: "lastWeekdayOfMonth",
      value: "9",
    };
  }
}
