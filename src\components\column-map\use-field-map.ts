import { toRaw, ref, computed, reactive, onMounted } from "vue";
import {
  LineData,
  SourceTargetMap,
  TableDirctionMap,
  showLineData,
} from "./types";
import {
  isLineDataValid,
  getLinePosition,
  isLineDataSame,
  getAnchorPosition,
} from "./utils";

const defaultLineValue: LineData = {
  table: "",
  field: "",
};

const defaultCurrentLine = {
  x1: 0,
  y1: 0,
  x2: 0,
  y2: 0,
};

export default function useFieldMap(props: any, emits: any) {
  let sourceItem: LineData = defaultLineValue;
  let targetItem: LineData = defaultLineValue;
  const tableDirctionMap: TableDirctionMap = [];
  const containerRef: any = ref(null);
  const isDragging = ref(false);
  const tableMaps = ref<SourceTargetMap[]>([]);
  const moveCount = ref(1);
  const currentLine = reactive({ ...defaultCurrentLine });
  const lines = computed(() => {
    const result: {
      position: { x1: number; y1: number; x2: number; y2: number };
      data: SourceTargetMap;
    }[] = [];
    if (!moveCount.value) return result;
    tableMaps.value.forEach((map) => {
      const position = getLinePosition(map.source, map.target);
      result.push({
        position,
        data: map,
      });
    });
    return result;
  });

  function handleTableMove() {
    setTimeout(() => moveCount.value++, 0);
  }

  function createLine(source: LineData, target: LineData) {
    tableMaps.value.push({
      source: { table: source.table, field: source.field },
      target: { table: target.table, field: target.field },
    });
  }

  function startConnect(sourceId: string, tableName: string) {
    sourceItem = {
      table: tableName,
      field: sourceId,
    };
    isDragging.value = true;
    // 设置currentLine的虚线
    const { x, y } = getAnchorPosition(tableName, sourceId, "right");
    currentLine.x1 = x;
    currentLine.y1 = y;
    currentLine.x2 = x;
    currentLine.y2 = y;
  }

  function endConnect(targetId: string, tableName: string) {
    isDragging.value = false;
    hideDashLine();
    const targetTmp = {
      table: tableName,
      field: targetId,
    };
    if (
      !isLineDataValid(sourceItem, targetTmp) ||
      isLineExists(sourceItem, targetTmp)
    )
      return (sourceItem = defaultLineValue);
    targetItem = targetTmp;
    createLine(sourceItem, targetTmp);
    sourceItem = defaultLineValue;
    targetItem = defaultLineValue;
    emitChange();
  }

  function emitChange() {
    handleTableMove();
    const data = toRaw({
      lines: tableMaps.value,
      tableDirctionMap,
    });
    emits("change", data);
  }
  // 判断Line是否存在
  function isLineExists(source: LineData, target: LineData) {
    return tableMaps.value.some(
      (item) =>
        isLineDataSame(item.source, source) &&
        isLineDataSame(item.target, target)
    );
  }

  function removeLine(data: { source: LineData; target: LineData }) {
    tableMaps.value = tableMaps.value.filter((item) =>
      isLineDataSame(item.source, data.source) &&
      isLineDataSame(item.target, data.target)
        ? false
        : true
    );
    emitChange();
  }

  function clearAllLines() {
    tableMaps.value = [];
    emitChange();
  }

  function showDashLine(event: MouseEvent) {
    if (!isDragging.value) return;
    const { x, y } = containerRef.value.getBoundingClientRect();
    currentLine.x2 = event.clientX - x;
    currentLine.y2 = event.clientY - y;
  }

  function hideDashLine() {
    if (isDragging.value) isDragging.value = false;
    Object.assign(currentLine, defaultCurrentLine);
  }
  return {
    createLine,
    tableMaps,
    isDragging,
    sourceItem,
    targetItem,
    lines,
    handleTableMove,
    startConnect,
    endConnect,
    removeLine,
    showDashLine,
    hideDashLine,
    clearAllLines,
    emitChange,
    tableDirctionMap,
    currentLine,
    containerRef,
  };
}
