<template>
  <UseDraggable :handle="header" v-slot="{ x, y }" :initial-value="props.position as any" class="table"
    @move="handleMove">
    <div ref="header" class="header">
      <slot name="header">
        👋 Drag here!
      </slot>
    </div>
    <div class="column">
      <!-- <slot name="column"> -->
      <div v-for="item in props.fields" class="row" :id="getRawId({ tableName: props.tableName, rowId: item.name })"
        @mousedown.prevent="handleDragStart(item)" style="user-select: none;" @mouseup.prevent="handleDragEnd(item)">

        <div class="left-anchor row-anchor"
          :id="getAnchorId({ tableName: props.tableName, rowId: item.name, direction: 'left' })"></div>

        <slot name="row" :item="item">
          <div class="content">{{ item.name }}({{ item.type }})</div>
        </slot>
        <div class="right-anchor row-anchor"
          :id="getAnchorId({ tableName: props.tableName, rowId: item.name, direction: 'right' })"></div>
      </div>
      <!-- </slot> -->
    </div>
  </UseDraggable>

</template>

<script setup lang="ts">
import { ref } from 'vue'
import { UseDraggable } from '@vueuse/components'
import { getAnchorId, getRawId } from './utils'

const header = ref(null)

const props = defineProps({
  tableName: {
    type: String,
    required: true,
  },
  fields: {
    type: Array<any>,
    required: true,
    default: () => []
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  }
})

const emits = defineEmits(['startConnect', 'endConnect', 'move'])

function handleDragStart(item: any) {
  emits('startConnect', item.name)
}

function handleDragEnd(item: any) {
  emits('endConnect', item.name)
}

function handleMove(e: any) {
  emits('move', e)
}
</script>

<style lang="scss" scoped>
.table {
  width: 300px;
  height: 500px;
  display: flex;
  flex-direction: column;
  position: absolute;
  border: 2px solid black;
  z-index: 1;

  &:active {
    z-index: 10;
  }

  .header {
    cursor: move;
    width: 100%;
    height: 50px;
    background-color: #98f3ee;
  }

  .column {
    flex: 1;
    height: 100%;
    background-color: #817f7f;
    z-index: 1;

    .row {
      background-color: aquamarine;
      border: 1px solid black;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      z-index: 2;

      .content {
        flex: 1
      }

      .row-anchor {
        width: 10px;
        height: 10px;
        // position: absolute;
        background-color: red;
      }

      .left-anchor {
        margin-left: -10px;
      }

      .right-anchor {
        margin-right: -10px;
      }
    }
  }
}
</style>