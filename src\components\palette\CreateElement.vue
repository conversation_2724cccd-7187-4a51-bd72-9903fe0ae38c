<template>
  <div class="create-element-container" @click="createElement($event, props.nodeType as NodeTypes)">
    <div class="left" :style="leftStyle"></div>
    <div class="mid-icon" :style="iconStyle"></div>
    <div class="right-text">{{ rightText }}</div>
  </div>
  <a-button type="secondary" @click="testUpdate">test</a-button>
</template>

<script lang="ts" setup>
// 实现自定义属性面板
import { computed } from 'vue';
import useModelerStore from '@/store/modeler'
import ElementFactory from 'bpmn-js/lib/features/modeling/ElementFactory'
import Create from 'diagram-js/lib/features/create/Create'
import { NodeTypes, config as renderConfig } from '@/renderer/config'

const modelerStore = useModelerStore()

const createElement = (ev: Event, nodeType: NodeTypes) => {
  const ElementFactory: ElementFactory = modelerStore.getModeler!.get('elementFactory')
  const moddle: any = modelerStore.getModeler!.get('moddle')
  const create: Create = modelerStore.getModeler!.get('create')
  const nodeConfig = renderConfig[nodeType]

  const shape = ElementFactory.createShape({
    // type: `bpmn:${nodeConfig.bpmnType}`,
    id: 'test-id',
    type: `bpmn:${nodeConfig.bpmnType}`,
    nodeType,
    width: nodeConfig.style.width,
    height: nodeConfig.style.height,
  })
  console.log('%c [ shape ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', shape)

  create.start(ev, shape)
}

function testUpdate() {
  const modeling: any = modelerStore.getModeler!.get('modeling')
  const testElement = elementRegistry.get('test-id')
  console.log('%c [ testElement ]-43', 'font-size:13px; background:pink; color:#bf2c9f;', testElement)
  // 执行更新
  modeling.updateProperties(testElement, {
    script: 'test-scirpt',
  })
  console.log('%c [ testElement ]-43', 'font-size:13px; background:pink; color:#bf2c9f;', testElement)
}

const props = defineProps({
  nodeType: {
    type: String,
    required: true,
  }
})

const nodeConfig: any = computed<any>(() => {
  return renderConfig[props.nodeType as NodeTypes]
})
const leftStyle: string = `background-color: ${nodeConfig.value.style.color};`;
const iconStyle = `background-image: url(${nodeConfig.value.style.icon});`
const rightText = nodeConfig.value.style.textContent;

</script>

<style lang="scss" scoped>
.create-element-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 50px;
  cursor: pointer;
  // 设置元素间距
  gap: 10px;
  border: 1px solid #000;

  .left {
    width: 25px;
    height: 100%;
  }

  .mid-icon {
    width: 50px;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .right-text {
    font-size: 14px;
    color: #333;
    text-align: center;
    width: 100px;
  }
}
</style>