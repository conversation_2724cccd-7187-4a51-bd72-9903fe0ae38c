import { defineStore } from 'pinia'
import type { Moddle } from 'moddle'
import type Modeler from 'bpmn-js/lib/Modeler'
import type Modeling from 'bpmn-js/lib/features/modeling/Modeling'
import type Canvas from 'diagram-js/lib/core/Canvas'
import type ElementRegistry from 'diagram-js/lib/core/ElementRegistry'
import { toRaw } from 'vue'
import { realXml2, realXml, xmlStr2 } from '../components/data/xmlStr';

// 联合类型
type ModelerRef = Modeler | undefined
type DebugStore = {
  activeElement: BpmnElement | undefined
  activeElementId: string | undefined
  activeVariables: { variableName: string, variableValue: string, type: string, valueInfo: any }[]
  debugModeler: ModelerRef
  editModeler: ModelerRef
  isDebugMode: boolean
  isRunning: boolean
  xmlStr: string
  processDefId: string
  dataId: string | undefined
  breakpoints: any[]
}

const defaultState: DebugStore = {
  activeElement: undefined,
  activeElementId: undefined,
  activeVariables: [],
  isDebugMode: false,
  isRunning: false,
  debugModeler: undefined,
  editModeler: undefined,
  processDefId: 'Process_15y9jhq:2:135',
  dataId: undefined,
  xmlStr: realXml,
  breakpoints: []
}

export default defineStore('modeler', {
  state: (): DebugStore => defaultState,
  getters: {
    getActive: (state) => toRaw(state.activeElement),
    getActiveId: (state) => state.activeElementId,
    getIsDebugMode: (state) => state.isDebugMode,
    getDebugModeler: (state) => toRaw(state.debugModeler),
    getEditModeler: (state) => toRaw(state.editModeler),
    getDataId: (state) => state.dataId,
    getModeler: (state) => state.isDebugMode ? toRaw(state.debugModeler) : toRaw(state.editModeler),
    getXmlStr: (state) => state.xmlStr,
    getProcessDefId: (state) => state.processDefId,
  },
  actions: {
    setModeler(debugModeler: ModelerRef, editModeler: ModelerRef) {
      this.debugModeler = debugModeler
      this.editModeler = editModeler
    },
    setProcessDefId(processDefId: string) {
      this.processDefId = processDefId
    },
    setXmlStr(xmlStr: string) {
      this.xmlStr = xmlStr
    },
    async changeMode(isDebugMode: boolean) {
      console.log('%c [ changeMode ]-64', 'font-size:13px; background:pink; color:#bf2c9f;', isDebugMode)
      // 判断模式是否有变化
      if (this.isDebugMode === isDebugMode) {
        return
      }
      const { xml, error } = await this.getModeler!.saveXML()
      if (xml) {
        this.xmlStr = xml
        this.isDebugMode = isDebugMode
      } else {
        throw error
      }
      // 切换模式时，清空选中元素
      this.activeElement = undefined
      this.activeElementId = undefined
      await this.loadDiagram()
    },
    setElement(element: BpmnElement | undefined) {
      this.activeElement = element
      this.activeElementId = element?.id
    },
    async loadDiagram() {
      this.getModeler!.importXML(this.xmlStr)
    }
  }
})
