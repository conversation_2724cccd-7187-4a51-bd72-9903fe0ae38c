<template>
  <div class="enhanced-demo">
    <!-- 标题和说明 -->
    <div class="demo-header">
      <h2>增强版拖拽表格演示</h2>
      <div class="instructions">
        <h3>功能特性：</h3>
        <ul>
          <li>
            <strong>智能拖拽：</strong>拖拽到禁用行时自动找到最近的有效位置
          </li>
          <li><strong>视觉反馈：</strong>实时显示插入位置（上方/下方）</li>
          <li><strong>禁用行保护：</strong>禁用行位置永远不变</li>
          <li><strong>精确控制：</strong>支持在行的上半部分或下半部分放置</li>
        </ul>

        <h3>测试场景：</h3>
        <div class="test-scenarios">
          <div class="scenario">
            <strong>场景1：</strong>将"项目1"拖拽到"项目3(禁用)"上方 →
            应该插入到项目2和项目3之间
          </div>
          <div class="scenario">
            <strong>场景2：</strong>将"项目1"拖拽到"项目4(禁用)"下方 →
            应该插入到项目4和项目5之间
          </div>
          <div class="scenario">
            <strong>场景3：</strong>将"项目5"拖拽到"项目6"上方 → 应该交换位置
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <button @click="resetData" class="btn btn-primary">重置数据</button>
      <button @click="toggleDebug" class="btn btn-secondary">
        {{ debugMode ? "关闭" : "开启" }}调试模式
      </button>
      <button @click="addRandomItem" class="btn btn-success">
        添加随机项目
      </button>
      <button @click="toggleRandomDisabled" class="btn btn-warning">
        随机禁用/启用
      </button>
    </div>

    <!-- 增强版拖拽表格 -->
    <EnhancedDragTable
      v-model:data="tableData"
      :columns="tableColumns"
      :debug="debugMode"
      key-field="id"
      display-field="name"
      disabled-field="disabled"
      @row-reorder="handleRowReorder"
    >
      <!-- 状态列模板 -->
      <template #status="{ data }">
        <span
          class="status-badge"
          :class="data.disabled ? 'status-disabled' : 'status-enabled'"
        >
          {{ data.disabled ? "禁用" : "启用" }}
        </span>
      </template>

      <!-- 操作列模板 -->
      <template #actions="{ data, index }">
        <div class="action-buttons">
          <button
            @click="toggleItemStatus(index)"
            class="btn btn-sm"
            :class="data.disabled ? 'btn-success' : 'btn-warning'"
          >
            {{ data.disabled ? "启用" : "禁用" }}
          </button>
          <button @click="deleteItem(index)" class="btn btn-sm btn-danger">
            删除
          </button>
        </div>
      </template>
    </EnhancedDragTable>

    <!-- 数据状态显示 -->
    <div class="data-display">
      <h3>当前数据状态：</h3>
      <div class="data-list">
        <div
          v-for="(item, index) in tableData"
          :key="item.id"
          class="data-item"
          :class="{ disabled: item.disabled }"
        >
          <span class="item-index">{{ index + 1 }}.</span>
          <span class="item-name">{{ item.name }}</span>
          <span class="item-status"
            >({{ item.disabled ? "禁用" : "启用" }})</span
          >
        </div>
      </div>
    </div>

    <!-- 操作历史 -->
    <div class="operation-history" v-if="operationHistory.length > 0">
      <h3>操作历史：</h3>
      <div class="history-list">
        <div
          v-for="(operation, index) in operationHistory.slice(-5)"
          :key="index"
          class="history-item"
        >
          <span class="history-time">{{ operation.time }}</span>
          <span class="history-action">{{ operation.action }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import EnhancedDragTable from "./enhanced-drag-table.vue";

// ==================== 数据定义 ====================
interface TableItem {
  id: number;
  name: string;
  description: string;
  disabled: boolean;
  priority: string;
}

interface OperationRecord {
  time: string;
  action: string;
}

// ==================== 响应式数据 ====================
const debugMode = ref(false);
const nextId = ref(7);

const tableData = ref<TableItem[]>([
  {
    id: 1,
    name: "项目1",
    description: "第一个项目",
    disabled: false,
    priority: "高",
  },
  {
    id: 2,
    name: "项目2",
    description: "第二个项目",
    disabled: false,
    priority: "中",
  },
  {
    id: 3,
    name: "项目3",
    description: "第三个项目（禁用）",
    disabled: true,
    priority: "低",
  },
  {
    id: 4,
    name: "项目4",
    description: "第四个项目（禁用）",
    disabled: true,
    priority: "中",
  },
  {
    id: 5,
    name: "项目5",
    description: "第五个项目",
    disabled: false,
    priority: "高",
  },
  {
    id: 6,
    name: "项目6",
    description: "第六个项目",
    disabled: false,
    priority: "低",
  },
]);

const tableColumns = [
  { field: "name", header: "项目名称", style: { minWidth: "150px" } },
  { field: "description", header: "描述", style: { minWidth: "200px" } },
  { field: "priority", header: "优先级", style: { width: "100px" } },
  {
    field: "status",
    header: "状态",
    template: "status",
    style: { width: "100px" },
  },
  {
    field: "actions",
    header: "操作",
    template: "actions",
    style: { width: "150px" },
  },
];

const operationHistory = ref<OperationRecord[]>([]);

// ==================== 工具函数 ====================
const addOperationRecord = (action: string) => {
  operationHistory.value.push({
    time: new Date().toLocaleTimeString(),
    action,
  });
};

const resetData = () => {
  tableData.value = [
    {
      id: 1,
      name: "项目1",
      description: "第一个项目",
      disabled: false,
      priority: "高",
    },
    {
      id: 2,
      name: "项目2",
      description: "第二个项目",
      disabled: false,
      priority: "中",
    },
    {
      id: 3,
      name: "项目3",
      description: "第三个项目（禁用）",
      disabled: true,
      priority: "低",
    },
    {
      id: 4,
      name: "项目4",
      description: "第四个项目（禁用）",
      disabled: true,
      priority: "中",
    },
    {
      id: 5,
      name: "项目5",
      description: "第五个项目",
      disabled: false,
      priority: "高",
    },
    {
      id: 6,
      name: "项目6",
      description: "第六个项目",
      disabled: false,
      priority: "低",
    },
  ];
  nextId.value = 7;
  operationHistory.value = [];
  addOperationRecord("重置数据到初始状态");
};

const toggleDebug = () => {
  debugMode.value = !debugMode.value;
  addOperationRecord(`${debugMode.value ? "开启" : "关闭"}调试模式`);
};

const addRandomItem = () => {
  const priorities = ["高", "中", "低"];
  const randomPriority =
    priorities[Math.floor(Math.random() * priorities.length)];

  const newItem: TableItem = {
    id: nextId.value++,
    name: `项目${nextId.value - 1}`,
    description: `随机添加的项目`,
    disabled: Math.random() > 0.7, // 30% 概率禁用
    priority: randomPriority,
  };

  tableData.value.push(newItem);
  addOperationRecord(`添加新项目: ${newItem.name}`);
};

const toggleRandomDisabled = () => {
  const enabledItems = tableData.value.filter((item) => !item.disabled);
  if (enabledItems.length > 0) {
    const randomItem =
      enabledItems[Math.floor(Math.random() * enabledItems.length)];
    randomItem.disabled = !randomItem.disabled;
    addOperationRecord(
      `切换 ${randomItem.name} 的状态为 ${
        randomItem.disabled ? "禁用" : "启用"
      }`
    );
  }
};

const toggleItemStatus = (index: number) => {
  const item = tableData.value[index];
  item.disabled = !item.disabled;
  addOperationRecord(
    `手动切换 ${item.name} 状态为 ${item.disabled ? "禁用" : "启用"}`
  );
};

const deleteItem = (index: number) => {
  const item = tableData.value[index];
  tableData.value.splice(index, 1);
  addOperationRecord(`删除项目: ${item.name}`);
};

// ==================== 事件处理 ====================
const handleRowReorder = (event: { originalEvent: Event; value: any[] }) => {
  const oldOrder = tableData.value.map((item) => item.name).join(", ");
  const newOrder = event.value.map((item) => item.name).join(", ");

  addOperationRecord(`拖拽重排序: [${oldOrder}] → [${newOrder}]`);

  console.log("拖拽重排序事件:", {
    oldOrder,
    newOrder,
    data: event.value,
  });
};
</script>

<style scoped>
/* ==================== 基础布局 ==================== */
.enhanced-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.demo-header {
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 600;
}

/* ==================== 说明区域 ==================== */
.instructions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.instructions ul {
  margin-bottom: 20px;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.test-scenarios {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.scenario {
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.scenario:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* ==================== 控制面板 ==================== */
.control-panel {
  display: flex;
  gap: 12px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #1e7e34;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* ==================== 状态徽章 ==================== */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 50px;
}

.status-enabled {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-disabled {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* ==================== 操作按钮 ==================== */
.action-buttons {
  display: flex;
  gap: 6px;
}

/* ==================== 数据显示区域 ==================== */
.data-display {
  margin-top: 30px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.data-display h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
  font-size: 18px;
}

.data-list {
  display: grid;
  gap: 8px;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.data-item.disabled {
  background-color: #f5f5f5;
  opacity: 0.7;
}

.item-index {
  font-weight: 600;
  color: #6c757d;
  margin-right: 8px;
  min-width: 25px;
}

.item-name {
  font-weight: 500;
  color: #495057;
  margin-right: 8px;
}

.item-status {
  color: #6c757d;
  font-size: 14px;
}

/* ==================== 操作历史 ==================== */
.operation-history {
  margin-top: 30px;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.operation-history h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
  font-size: 18px;
}

.history-list {
  display: grid;
  gap: 6px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.history-time {
  font-family: monospace;
  font-size: 12px;
  color: #6c757d;
  margin-right: 12px;
  min-width: 80px;
}

.history-action {
  font-size: 14px;
  color: #495057;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .enhanced-demo {
    padding: 15px;
  }

  .control-panel {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
  }

  .data-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .item-index,
  .item-name,
  .item-status {
    margin-right: 0;
    margin-bottom: 4px;
  }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.data-item,
.history-item {
  animation: fadeIn 0.3s ease;
}
</style>
