<template>
  <div class="timeline-container" :style="{ width: `${viewWidth}px`, overflow: 'hidden' }">
    <div class="gantt-header time-line"
      :style="{ width: `${rowWidth}px`, transform: `translateX(${timeLineTransformX}px)` }">
      <div class="time-line-item" v-for="item in timeLine" :key="item.timeStr" :style="{ width: `${timeStepWidth}px` }">
        {{ item.timeStr }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject, ComputedRef } from 'vue'
import dayjs from 'dayjs'

const stepTime = inject('stepTime') as ComputedRef<number>
const viewWidth = inject('viewWidth') as ComputedRef<number>
const startDate = inject('startDate') as ComputedRef<number>
const rowWidth = inject('rowWidth') as ComputedRef<number>
const originTimeRangeStamp = inject('originTimeRangeStamp') as ComputedRef<number>


const timeLine = computed(() => {
  const stepWidth = (stepTime.value / originTimeRangeStamp.value) * rowWidth.value
  const timeLineArr: any[] = []
  for (let i = 0; i < stepCount.value; i++) {
    const time = startDate.value + (i * stepTime.value)
    // 显示时间 只显示小时
    const timeStr = dayjs(time).format('HH:mm')
    timeLineArr.push({
      timeStr: timeStr,
      left: i * stepWidth,
    })
  }
  return timeLineArr
})
const stepCount = computed(() => {
  return Math.ceil(originTimeRangeStamp.value / stepTime.value)
})

const timeStepWidth = computed(() => {
  return rowWidth.value / stepCount.value
})
const timeLineTransformX = ref(0)

function setTransformX(x: number) {
  timeLineTransformX.value = x
}
defineExpose({
  setTransformX,
})
</script>

<style lang="scss">
.time-line {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
  z-index: 1;

  .time-line-item {
    height: 20px;
    color: black;
    text-align: left;
  }
}
</style>