<template>
  <Teleport to="body">
    <transition name="g-fade" mode="out-in">
      <div v-if="showTooltip" class="gantt-tooltip" :style="{
        top: tooltipTop,
        left: tooltipLeft,
      }">
        <slot :bar="barData">
          <div class="gantt-tooltip-content">
            <div v-for="item in formatData" class="gantt-tooltip-text">
              <div class="label">{{ item.label }}</div>
              <div class="content">{{ item.value }}</div>
            </div>
          </div>
        </slot>
      </div>
    </transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, toRefs, ref, watch, nextTick, inject } from "vue"
import { getTaskId } from './utils'

// const props = defineProps({
//   barData: {
//     type: Object,
//     default: () => ({
//     }),
//   },
// })

const barData: any = inject('tooltipData')

const showTooltip = ref(false)
const tooltipTop = ref("0px")
const tooltipLeft = ref("0px")

watch(
  barData,
  (value) => {
    console.log('%c [ value ]-42', 'font-size:13px; background:pink; color:#bf2c9f;', value)
    if (!value) {
      return showTooltip.value = false
    }
    showTooltip.value = true
    // bar更新时 获取新的bar的位置 并更新tooltip位置
    const { rowId, label, startTime, endTime } = value
    const taskId = getTaskId(rowId, {
      label, startTime, endTime
    })
    const taskEl = document.querySelector(`#${taskId}`)
    if (taskEl) {
      const { top, left } = taskEl.getBoundingClientRect() || { top: 0, left: 0 }
      tooltipTop.value = `${top + 20}px`
      tooltipLeft.value = `${left + 20}px`
    }
  }
)

const formatData = computed(() => {
  return [
    {
      label: '任务名称',
      value: barData.value.label
    },
    {
      label: '开始时间',
      value: barData.value.startTime
    },
    {
      label: '结束时间',
      value: barData.value.endTime
    },

  ]
})
</script>

<style lang="scss">
.gantt-tooltip {
  position: absolute;
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  z-index: 9999;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
  height: 80px;
  // width: 180px;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
  color: #212529;
  border-radius: 14px 14px 14px 14px;

  &.g-fade-enter-active,
  &.g-fade-leave-active {
    transition: opacity 0.3s ease-in-out;
  }

  .gantt-tooltip-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    font-size: 12px;

    .gantt-tooltip-text {
      display: flex;
      align-items: center;
      width: 100%;
      height: 16px;

      &:not(:last-child) {
        margin-bottom: 4px;
      }

      .label {
        color: #595C5F;
        width: 64px;
      }

      .content {}
    }
  }
}
</style>