<template>
  <div class="simple-flow-editor w-full h-full flex">
    <!-- 左侧节点面板 -->
    <div class="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
      <!-- 面板标题 -->
      <div class="p-4 border-b border-gray-200 bg-white">
        <h3 class="text-lg font-semibold text-gray-800">节点面板</h3>
      </div>
      
      <!-- 搜索框 -->
      <div class="p-4 border-b border-gray-200 bg-white">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索节点..."
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>
      
      <!-- 节点分类 -->
      <div class="flex-1 overflow-y-auto">
        <div
          v-for="category in filteredCategories"
          :key="category.name"
          class="border-b border-gray-200"
        >
          <!-- 分类标题 -->
          <button
            @click="toggleCategory(category.name)"
            class="w-full flex items-center justify-between p-4 text-left hover:bg-gray-100 transition-colors"
          >
            <span class="font-medium text-gray-800">{{ getCategoryLabel(category.name) }}</span>
            <i
              :class="expandedCategories.has(category.name) ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
              class="text-gray-400"
            />
          </button>
          
          <!-- 节点列表 -->
          <div
            v-if="expandedCategories.has(category.name)"
            class="bg-gray-50"
          >
            <div
              v-for="nodeType in category.nodes"
              :key="nodeType.id"
              :draggable="true"
              @dragstart="handleDragStart(nodeType, $event)"
              class="flex items-center p-3 mx-2 mb-2 bg-white rounded-lg border border-gray-200 cursor-move hover:shadow-md transition-all duration-200 hover:border-blue-300"
            >
              <!-- 节点图标 -->
              <div
                class="flex items-center justify-center w-10 h-10 rounded-lg mr-3"
                :style="{ backgroundColor: nodeType.color + '20', color: nodeType.color }"
              >
                <i :class="nodeType.icon || 'pi pi-circle'" class="text-lg" />
              </div>
              
              <!-- 节点信息 -->
              <div class="flex-1 min-w-0">
                <div class="font-medium text-gray-800 truncate">{{ nodeType.name }}</div>
                <div class="text-sm text-gray-500 truncate">{{ nodeType.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间画布区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 工具栏 -->
      <div class="h-12 bg-white border-b border-gray-200 flex items-center justify-between px-4">
        <div class="flex items-center space-x-2">
          <button
            @click="saveFlow"
            class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            <i class="pi pi-save mr-1" />
            保存
          </button>
          <button
            @click="loadSampleFlow"
            class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            <i class="pi pi-play mr-1" />
            示例
          </button>
          <button
            @click="clearFlow"
            class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            <i class="pi pi-trash mr-1" />
            清空
          </button>
        </div>
        
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">节点: {{ nodes.length }}</span>
          <span class="text-sm text-gray-600">连线: {{ edges.length }}</span>
        </div>
      </div>
      
      <!-- 画布 -->
      <div class="flex-1 relative bg-gray-100">
        <div
          ref="canvasRef"
          @drop="handleDrop"
          @dragover.prevent
          @dragenter.prevent
          class="w-full h-full bg-white"
          style="background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px); background-size: 20px 20px;"
        >
          <!-- X6 画布将在这里渲染 -->
        </div>
        
        <!-- 画布提示 -->
        <div
          v-if="nodes.length === 0"
          class="absolute inset-0 flex items-center justify-center pointer-events-none"
        >
          <div class="text-center text-gray-500">
            <div class="text-6xl mb-4">🎨</div>
            <div class="text-xl font-medium mb-2">开始创建流程</div>
            <div class="text-sm">从左侧拖拽节点到画布，或点击"示例"查看演示</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧属性面板 -->
    <div class="w-80 bg-gray-50 border-l border-gray-200 flex flex-col">
      <!-- 面板标题 -->
      <div class="p-4 border-b border-gray-200 bg-white">
        <h3 class="text-lg font-semibold text-gray-800">属性面板</h3>
      </div>
      
      <!-- 属性内容 -->
      <div class="flex-1 p-4">
        <div v-if="selectedNode" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">节点标签</label>
            <input
              v-model="selectedNode.label"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">X坐标</label>
              <input
                v-model.number="selectedNode.x"
                type="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Y坐标</label>
              <input
                v-model.number="selectedNode.y"
                type="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
        
        <div v-else class="text-center text-gray-500 mt-8">
          <i class="pi pi-info-circle text-4xl mb-4" />
          <p>选择节点查看属性</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Graph } from '@antv/x6'
import type { FlowNodeType } from './types'
import { nodeTypeRegistry } from './nodes/simpleNodeTypes'

// 响应式数据
const searchQuery = ref('')
const expandedCategories = ref(new Set(['control', 'process']))
const canvasRef = ref<HTMLDivElement>()
const graph = ref<Graph>()
const nodes = ref<any[]>([])
const edges = ref<any[]>([])
const selectedNode = ref<any>(null)

// 获取所有节点类型
const allNodeTypes = computed(() => nodeTypeRegistry.getAll())

// 获取所有分类
const categories = computed(() => {
  const cats = new Set<string>()
  allNodeTypes.value.forEach(type => cats.add(type.category))
  return Array.from(cats)
})

// 过滤后的分类和节点
const filteredCategories = computed(() => {
  const query = searchQuery.value.toLowerCase()
  
  return categories.value.map(category => {
    const categoryNodes = allNodeTypes.value.filter(type => {
      const matchesCategory = type.category === category
      const matchesSearch = !query || 
        type.name.toLowerCase().includes(query) ||
        type.description?.toLowerCase().includes(query)
      
      return matchesCategory && matchesSearch
    })
    
    return {
      name: category,
      nodes: categoryNodes
    }
  }).filter(category => category.nodes.length > 0)
})

// 方法
const toggleCategory = (category: string) => {
  if (expandedCategories.value.has(category)) {
    expandedCategories.value.delete(category)
  } else {
    expandedCategories.value.add(category)
  }
}

const getCategoryLabel = (category: string): string => {
  const labels: Record<string, string> = {
    control: '控制节点',
    process: '处理节点',
    event: '事件节点',
    data: '数据节点',
    integration: '集成节点'
  }
  return labels[category] || category
}

const handleDragStart = (nodeType: FlowNodeType, event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'node',
      nodeType: nodeType.id,
      data: nodeType
    }))
    event.dataTransfer.effectAllowed = 'copy'
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (!graph.value) return
  
  try {
    const data = JSON.parse(event.dataTransfer?.getData('application/json') || '{}')
    if (data.type === 'node') {
      const rect = canvasRef.value!.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      createNode(data.data, { x, y })
    }
  } catch (error) {
    console.error('Drop failed:', error)
  }
}

const createNode = (nodeType: FlowNodeType, position: { x: number, y: number }) => {
  if (!graph.value) return
  
  const node = graph.value.addNode({
    id: `node_${Date.now()}`,
    shape: getNodeShape(nodeType.id),
    x: position.x - 60,
    y: position.y - 30,
    width: nodeType.defaultSize?.width || 120,
    height: nodeType.defaultSize?.height || 60,
    label: nodeType.name,
    attrs: {
      body: {
        fill: nodeType.color,
        stroke: '#333',
        strokeWidth: 2,
        rx: nodeType.id === 'start' || nodeType.id === 'end' ? 30 : 8
      },
      text: {
        fill: '#fff',
        fontSize: 12,
        fontWeight: 'bold'
      }
    }
  })
  
  nodes.value.push(node)
}

const getNodeShape = (nodeType: string): string => {
  switch (nodeType) {
    case 'start':
    case 'end':
      return 'circle'
    case 'decision':
      return 'polygon'
    default:
      return 'rect'
  }
}

const initGraph = () => {
  if (!canvasRef.value) return
  
  graph.value = new Graph({
    container: canvasRef.value,
    width: canvasRef.value.clientWidth,
    height: canvasRef.value.clientHeight,
    grid: {
      size: 20,
      visible: true,
      type: 'doubleMesh',
      args: [
        { color: '#E7E8EA', thickness: 1 },
        { color: '#CBCED3', thickness: 1, factor: 4 }
      ]
    },
    selecting: {
      enabled: true,
      rubberband: true,
      movable: true,
      showNodeSelectionBox: true
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: { radius: 8 }
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      allowLoop: false,
      allowNode: false,
      allowEdge: false,
      allowMulti: false,
      createEdge() {
        return this.createEdge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8
              }
            }
          },
          zIndex: 0
        })
      }
    }
  })
  
  // 监听节点选择
  graph.value.on('node:click', ({ node }) => {
    selectedNode.value = {
      id: node.id,
      label: node.getLabel(),
      x: node.getPosition().x,
      y: node.getPosition().y
    }
  })
  
  // 监听画布点击
  graph.value.on('blank:click', () => {
    selectedNode.value = null
  })
}

const saveFlow = () => {
  if (!graph.value) return
  
  const data = graph.value.toJSON()
  console.log('保存流程数据:', data)
  
  // 这里可以实现保存到服务器的逻辑
  alert('流程已保存到控制台')
}

const loadSampleFlow = () => {
  if (!graph.value) return
  
  // 清空现有内容
  graph.value.clearCells()
  nodes.value = []
  edges.value = []
  
  // 创建示例节点
  const startNode = graph.value.addNode({
    id: 'start',
    shape: 'circle',
    x: 100,
    y: 100,
    width: 80,
    height: 80,
    label: '开始',
    attrs: {
      body: { fill: '#4CAF50', stroke: '#388E3C', strokeWidth: 2 },
      text: { fill: '#fff', fontSize: 12, fontWeight: 'bold' }
    }
  })
  
  const processNode = graph.value.addNode({
    id: 'process',
    shape: 'rect',
    x: 250,
    y: 80,
    width: 120,
    height: 80,
    label: '处理',
    attrs: {
      body: { fill: '#2196F3', stroke: '#1976D2', strokeWidth: 2, rx: 8 },
      text: { fill: '#fff', fontSize: 12, fontWeight: 'bold' }
    }
  })
  
  const endNode = graph.value.addNode({
    id: 'end',
    shape: 'circle',
    x: 450,
    y: 100,
    width: 80,
    height: 80,
    label: '结束',
    attrs: {
      body: { fill: '#F44336', stroke: '#D32F2F', strokeWidth: 2 },
      text: { fill: '#fff', fontSize: 12, fontWeight: 'bold' }
    }
  })
  
  // 创建连线
  graph.value.addEdge({
    source: startNode,
    target: processNode,
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: { name: 'block', width: 12, height: 8 }
      }
    }
  })
  
  graph.value.addEdge({
    source: processNode,
    target: endNode,
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: { name: 'block', width: 12, height: 8 }
      }
    }
  })
  
  nodes.value = [startNode, processNode, endNode]
}

const clearFlow = () => {
  if (!graph.value) return
  
  graph.value.clearCells()
  nodes.value = []
  edges.value = []
  selectedNode.value = null
}

// 生命周期
onMounted(() => {
  initGraph()
  
  // 监听窗口大小变化
  const resizeObserver = new ResizeObserver(() => {
    if (graph.value && canvasRef.value) {
      graph.value.resize(canvasRef.value.clientWidth, canvasRef.value.clientHeight)
    }
  })
  
  if (canvasRef.value) {
    resizeObserver.observe(canvasRef.value)
  }
})

onUnmounted(() => {
  if (graph.value) {
    graph.value.dispose()
  }
})
</script>

<style scoped>
.simple-flow-editor {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 拖拽样式 */
[draggable="true"] {
  cursor: grab;
}

[draggable="true"]:active {
  cursor: grabbing;
}
</style>
