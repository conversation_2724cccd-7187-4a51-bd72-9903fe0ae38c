<template>
  <!-- <div class="custom-property-panel" v-show="modelerStore.activeElementId"> -->
  <div class="custom-property-panel">
    <DebugControl />
    <div class="debug-info">
      <a-collapse :default-active-key="['1', '2']">
        <a-collapse-item header="调试信息" key="1">
          <BreakpointInfo />
        </a-collapse-item>
        <a-collapse-item header="断点信息" key="2" style="padding: 0px;" class="breakpoint-info">
          <BreakpointList />
        </a-collapse-item>
      </a-collapse>
    </div>
    <a-button type="primary" @click="showModal"></a-button>
    <VariableEditor ref="variableEditor" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DebugControl from './components/DebugControl.vue';
import BreakpointInfo from './components/BreakpointInfo.vue';
import BreakpointList from './components/BreakpointList.vue';
import VariableEditor from './components/VariableEditor.vue'

const variableEditor = ref<any>(null);
function showModal() {
  variableEditor.value.visible = true;
}
</script>

<style lang="scss">
.custom-property-panel {
  height: 100%;
  width: 300px;
  background-color: #f5f5f5;
  padding-left: 16px;

}
</style>